# This file defines the package repositories and their permissions for the project.
# image-docker-hub: The Docker Hub repository for pulling images for testcontainers and related dependencies.
# maven: We need read and write permissions to Maven repository for publishing the project's artifacts.
packages:
  - repository: image-docker-hub
    permissions:
      - read
  - repository: maven
    permissions:
      - read
      - write:
          - 'com.lmig.usconsumermarkets.booktransfer.opportunity-service'
