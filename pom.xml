<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>bt-spring-boot-starter-parent</artifactId>
        <groupId>com.lmig.usconsumermarkets.booktransfer</groupId>
        <version>1.0.0</version>
    </parent>

	 <groupId>com.lmig.usconsumermarkets.booktransfer</groupId>
	<artifactId>opportunity-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
	<packaging>jar</packaging>

	<name>Opportunity Service</name>
	<description>Opportunity Service</description>

	<scm>
        <connection>scm:git:https://github.com/lmigtech/opportunityservice.git</connection>
        <developerConnection>scm:git:https://github.com/lmigtech/opportunityservice.git</developerConnection>
        <url>https://github.com/lmigtech/opportunityservice</url>
    </scm>
    
    <ciManagement>
        <system>GH Actions</system>
        <url>https://github.com/lmigtech/opportunityservice/actions</url>
    </ciManagement>
    
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <url>https://packages.lmig.com/artifactory/maven</url>
        </repository>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>https://packages.lmig.com/artifactory/maven-snapshots</url>
        </snapshotRepository>
    </distributionManagement>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<!-- LMIG owned -->
		<bt-mongo-starter.version>1.0.4</bt-mongo-starter.version>
		<bt-payment-service.version>0.0.1-main+69</bt-payment-service.version>
		<Rating-Service.version>0.0.1-master-326</Rating-Service.version>
		<quote-report-service.version>0.1.0-master-334</quote-report-service.version>
		<audit-log-rest-api.version>0.0.1-master+78</audit-log-rest-api.version>
		<bt-upload-preprocessor.version>0.0.1-main+29</bt-upload-preprocessor.version>
		<bt-quoting-guidelines-service.version>0.0.1-main+64</bt-quoting-guidelines-service.version>
		<utility-service.version>1.1.0-master-256</utility-service.version>
		<process-result-service.version>1.0.0-master+23</process-result-service.version>
		<book-transfer-service.version>1.0-master+66</book-transfer-service.version>
		<Email-Service.version>0.1.0-master-52</Email-Service.version>
		<bt-sensitive-data-service.version>0.0.1-master+80</bt-sensitive-data-service.version>
		<bt-security-starter.version>0.0.2-main+19</bt-security-starter.version>
		<transformation-service.version>0.0.1-master+166</transformation-service.version>
		<uploadmanager.version>0.0.2-master-543</uploadmanager.version>
		<spidataservice.version>0.1.0-master-46</spidataservice.version>
		<!-- others -->
		<joda-time.version>2.12.7</joda-time.version>
		<mapstruct.version>1.5.5.Final</mapstruct.version>
		<mapstruct-processor.version>1.5.5.Final</mapstruct-processor.version>
		<pitest-maven.version>1.4.0</pitest-maven.version>
		<commons-collections4.version>4.4</commons-collections4.version>
		<javatuples.version>1.2</javatuples.version>
		<deployment-artifacts-plugin.version>1.0.0</deployment-artifacts-plugin.version>
		<httpclient.version>4.5.14</httpclient.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.lmig.pi.booktransfer</groupId>
			<artifactId>uploadmanager</artifactId>
			<version>${uploadmanager.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
				<exclusion>
					<artifactId>jackson-dataformat-xml</artifactId>
					<groupId>com.fasterxml.jackson.dataformat</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-amqp</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.lmig.booktransfer</groupId>
			<artifactId>transformation-service</artifactId>
			<version>${transformation-service.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.lmig.pi.booktransfer</groupId>
			<artifactId>spi-policy-dataservice</artifactId>
			<version>${spidataservice.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<artifactId>commons-io</artifactId>
					<groupId>commons-io</groupId>
				</exclusion>
				<exclusion>
					<artifactId>springfox-swagger2</artifactId>
					<groupId>io.springfox</groupId>
				</exclusion>
				<exclusion>
					<artifactId>springfox-swagger-ui</artifactId>
					<groupId>io.springfox</groupId>
				</exclusion>
				<exclusion>
					<artifactId>salesforce-cert-utils</artifactId>
					<groupId>com.lmig.us-consumer-markets.lm-salesforce-innersource</groupId>
				</exclusion>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
				<exclusion>
					<artifactId>utility-service</artifactId>
					<groupId>com.lmig.pi.booktransfer</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.lmig.usconsumermarkets.booktransfer</groupId>
			<artifactId>bt-security-starter</artifactId>
			<version>${bt-security-starter.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.lmig.usconsumermarkets.booktransfer</groupId>
			<artifactId>bt-mongo-starter</artifactId>
			<version>${bt-mongo-starter.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-mongodb</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- End: Embedded Mongo dependencies for testing and local -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.lmig.us-consumer-markets.book-transfer</groupId>
			<artifactId>bt-payment-service</artifactId>
			<version>${bt-payment-service.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.lmig.pi.booktransfer</groupId>
			<artifactId>Rating-Service</artifactId>
			<version>${Rating-Service.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.lmig.pi.booktransfer</groupId>
			<artifactId>quote-report-service</artifactId>
			<version>${quote-report-service.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-compress</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>java-cfenv-boot</artifactId>
					<groupId>io.pivotal.cfenv</groupId>
				</exclusion>
				<exclusion>
					<groupId>joda-time</groupId>
					<artifactId>joda-time</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<!-- Prevents `java.lang.IllegalStateException: Failed to load ApplicationContext` -->
			<groupId>org.codehaus.janino</groupId>
			<artifactId>janino</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>${joda-time.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.lmig.pi.booktransfer</groupId>
			<artifactId>utility-service</artifactId>
			<version>${utility-service.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
			<version>${commons-collections4.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents.client5</groupId>
			<artifactId>httpclient5</artifactId>
			<scope>provided</scope>
			<!-- Version must be >4.3 for TLS communication -->
		</dependency>
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.lmig.uscm.booktransfer</groupId>
			<artifactId>process-result-service</artifactId>
			<version>${process-result-service.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.lmig.pi.booktransfer</groupId>
			<artifactId>book-transfer-service</artifactId>
			<version>${book-transfer-service.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.lmig.pi.booktransfer</groupId>
			<artifactId>Email-Service</artifactId>
			<version>${Email-Service.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>${mapstruct.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.lmig.booktransfer</groupId>
			<artifactId>bt-sensitive-data-service</artifactId>
			<version>${bt-sensitive-data-service.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<groupId>com.lmig.usconsumermarkets.booktransfer</groupId>
					<artifactId>bt-security-starter</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
			</exclusions>

		</dependency>
		<dependency>
			<groupId>com.lmig.us-consumer-markets.book-transfer</groupId>
			<artifactId>bt-quoting-guidelines-service</artifactId>
			<version>${bt-quoting-guidelines-service.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<artifactId>salesforce-cert-utils</artifactId>
					<groupId>com.lmig.us-consumer-markets.lm-salesforce-innersource</groupId>
				</exclusion>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.lmig.us-consumer-markets.book-transfer</groupId>
			<artifactId>audit-log-rest-api</artifactId>
			<version>${audit-log-rest-api.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<exclusions>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
				<exclusion>
					<artifactId>shedlock-provider-mongo</artifactId>
					<groupId>net.javacrumbs.shedlock</groupId>
				</exclusion>
				<exclusion>
					<groupId>net.javacrumbs.shedlock</groupId>
					<artifactId>shedlock-spring</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.lmig.us-consumer-markets.book-transfer</groupId>
			<artifactId>bt-upload-preprocessor</artifactId>
			<version>${bt-upload-preprocessor.version}</version>
			<classifier>client</classifier>
			<scope>provided</scope>
			<!-- TODO Remove these exclusions once the upload preprocessor upgraded to spring boot 3/Java 17-->
			<exclusions>
				<exclusion>
					<artifactId>springdoc-openapi-ui</artifactId>
					<groupId>org.springdoc</groupId>
				</exclusion>
				<exclusion>
					<artifactId>clover-maven-plugin</artifactId>
					<groupId>org.openclover</groupId>
				</exclusion>
				<exclusion>
					<artifactId>java-cfenv-boot</artifactId>
					<groupId>io.pivotal.cfenv</groupId>
				</exclusion>
				<exclusion>
					<artifactId>ciit-spring-boot-starter-ping</artifactId>
					<groupId>com.lmig.ci.devdisc</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.javatuples</groupId>
			<artifactId>javatuples</artifactId>
			<version>${javatuples.version}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-tracing</artifactId>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-tracing-bridge-brave</artifactId>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>localstack</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>junit-jupiter</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20240303</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.lmig.ssl</groupId>
			<artifactId>ssl-certificate-truster-common-certificates</artifactId>
			<version>3.0.1</version>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>mockwebserver</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.jacoco</groupId>
					<artifactId>jacoco-maven-plugin</artifactId>
					<version>0.8.8</version>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<!-- Used to compile JAVA 10+ -->
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
					<annotationProcessorPaths>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>${mapstruct-processor.version}</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok-mapstruct-binding</artifactId>
							<version>0.2.0</version>
						</path>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring.boot.version}</version>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.lmig.forge</groupId>
				<artifactId>deployment-artifacts-plugin</artifactId>
				<version>${deployment-artifacts-plugin.version}</version>
				<executions>
					<execution>
						<goals>
							<goal>attach</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-jar-plugin</artifactId>
				<version>${maven-jar-plugin.version}</version>
				<executions>
					<execution>
						<id>client</id>
						<phase>package</phase>
						<goals>
							<goal>jar</goal>
						</goals>
						<configuration>
							<classifier>client</classifier>
							<includes>
								<include>**/client/**</include>
								<include>**/client/**/*</include>
								<include>**/META-INF/**</include>
							</includes>
							<excludeScope>provided</excludeScope>
							<includeScope>test</includeScope>
							<test>test</test>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>1</id>
						<phase>generate-sources</phase>
						<!-- Executes the phase to run clover on -->
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>generate-code-coverage-report</id>
						<phase>test</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
					<execution>
						<!-- Jacoco Confifuration info:
                            https://www.eclemma.org/jacoco/trunk/doc/check-mojo.html
                            https://medium.com/@AyushVardhan/enforcing-code-coverage-rule-with-jacoco-in-maven-lifecycle-8ebc1fe3b6ce
                        -->
						<id>jacoco-check</id>
						<goals>
							<goal>check</goal>
						</goals>
						<configuration>
							<rules>
								<rule>
									<element>BUNDLE</element> <!-- options are  (BUNDLE, PACKAGE, CLASS, SOURCEFILE or METHOD)  -->
									<limits>
										<limit> <!-- Each limit applies to a certain counter (INSTRUCTION, LINE, BRANCH, COMPLEXITY, METHOD, CLASS) -->
											<counter>LINE</counter>
											<value>COVEREDRATIO</value>
											<minimum>50%</minimum>
										</limit>
										<limit>
											<counter>BRANCH</counter>
											<value>COVEREDRATIO</value>
											<minimum>0.15</minimum>
										</limit>
										<!-- Update missedcount to 0 after fixing coverage -->
										<limit>
											<counter>CLASS</counter>
											<value>MISSEDCOUNT</value>
											<maximum>1</maximum>
										</limit>
									</limits>
								</rule>
								<!--  Other rule type examples:
                                    <rule>
                                      <element>CLASS</element>
                                      <limits>
                                         <limit>
                                            <counter>LINE</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.80</minimum>
                                         </limit>
                                         <limit>
                                            <counter>BRANCH</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.80</minimum>
                                         </limit>
                                      </limits>
                                      <excludes>
                                         <exclude>com.abc.ClassName</exclude>
                                      </excludes>
                                   </rule>
                                 -->
							</rules>
						</configuration>
					</execution>
				</executions>
				<configuration>

					<excludes>
						<exclude>**/*test/java</exclude>
						<!-- Excludes test classes -->
						<exclude>**/*test/xml</exclude>
						<!-- Excludes test files -->
						<exclude>**/*Application.java</exclude>
						<exclude>**/*TestMockitoSetUp.java</exclude>
						<exclude>**/*BuildMockObjects.java</exclude>
						<exclude>**/*OpportunityTestUtil.java</exclude>
						<exclude>**/opportunity/services/PropertyInfoHelper.*</exclude>
						<exclude>**/config/</exclude>
						<exclude>**/domain/</exclude>
						<!-- TODO add coverage for these classes -->
						<exclude>**/opportunity/client/*</exclude>
						<exclude>**/opportunity/repo/mappers/OpportunityXmlDataMapper.*</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.pitest</groupId>
				<artifactId>pitest-maven</artifactId>
				<version>${pitest-maven.version}</version>
				<configuration>
					<excludedTestClasses>
						<param>com.lmig.uscm.booktransfer.opportunity.repo.*</param>
					</excludedTestClasses>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-release-plugin</artifactId>
				<version>${maven-release-plugin.version}</version>
				<configuration>
					<arguments>-DskipTests</arguments>
					<localCheckout>true</localCheckout>
					<tagNameFormat>v@{project.version}</tagNameFormat>
					<scmReleaseCommitComment>@{prefix} prepare release @{releaseLabel} [skip ci]</scmReleaseCommitComment>
					<scmDevelopmentCommitComment>@{prefix} prepare for next development iteration
						[skip ci]</scmDevelopmentCommitComment>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
