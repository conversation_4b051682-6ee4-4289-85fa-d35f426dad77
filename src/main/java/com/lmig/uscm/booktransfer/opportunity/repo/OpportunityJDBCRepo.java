/*
 * Copyright (c) 2019, Liberty Mutual
 * Proprietary and Confidential
 * All Rights Reserved
 */

package com.lmig.uscm.booktransfer.opportunity.repo;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.annotations.VisibleForTesting;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineOfBusiness;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityDetails;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityDisplay;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityErrorInfo;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityXmlData;
import com.lmig.uscm.booktransfer.opportunity.client.domain.ScheduleRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilter;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequestForOppIds;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.EffectiveDateRange;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityForScheduleResultExtractor;
import com.lmig.uscm.booktransfer.opportunity.repo.mappers.DownloadOpportunityRowMapper;
import com.lmig.uscm.booktransfer.opportunity.repo.mappers.OpportunityDetailsMapper;
import com.lmig.uscm.booktransfer.opportunity.repo.mappers.OpportunityErrorInfoMapper;
import com.lmig.uscm.booktransfer.opportunity.repo.mappers.OpportunityForEditRowMapper;
import com.lmig.uscm.booktransfer.opportunity.repo.mappers.OpportunityForStatusChangeRowMapper;
import com.lmig.uscm.booktransfer.opportunity.repo.mappers.OpportunityRowMapper;
import com.lmig.uscm.booktransfer.opportunity.repo.mappers.OpportunityXmlDataMapper;
import com.lmig.uscm.booktransfer.rating.client.domain.QuoteAndIssueUpdate;
import com.lmig.uscm.booktransfer.sql.domain.PaginationRequest;
import com.lmig.uscm.booktransfer.sql.helpers.FindFromDatabaseInList;
import com.lmig.uscm.booktransfer.sql.helpers.JPAHelper;
import com.lmig.uscm.booktransfer.sql.helpers.SQLQueryBuilder;
import com.lmig.uscm.booktransfer.utilityservice.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * this class job will be to take a Schedule and use that to find the opps that need quoted.
 */
@Service
@Slf4j
public class OpportunityJDBCRepo {

	public static final String OPPORTUNITY_ID_SQL_PARAM = "opportunityID";
	// This needs to make naming on custom filter please do not change
	public static final String OPPORTUNITY_IDS_SQL_PARAM = "opportunityIDs";
	public static final String BOOK_TRANSFER_ID_SQL_PARAM = "bookTransferId";
	// This needs to make naming on custom filter please do not change
	public static final String UPLOAD_EVENT_ID_SQL_PARAM = "uploadEventId";
	// This needs to make naming on custom filter please do not change
	public static final String LAST_POLICY_GUID_SQL_PARAM = "safecoPolicyGuid";

	public static final String FROM_OPPORTUNITIES_OPP_WITH_NOLOCK = "FROM Opportunities Opp with (nolock) ";
	public static final String SELECT = "SELECT * ";
	public static final String AND = " AND ";

	// Note: there is a fixed 2,100 parameter limit. Batching updates
	// https://learn.microsoft.com/en-us/sql/sql-server/maximum-capacity-specifications-for-sql-server?redirectedfrom=MSDN&view=sql-server-ver16
	public static final int SQL_BATCH_SIZE = 2000;
	public static final String WHERE_OPPORTUNITY_ID = " WHERE OpportunityId = ?";

	private final JdbcTemplate jdbcTemplate;
	private final NamedParameterJdbcTemplate parameterJdbcTemplate;
	private final OpportunityRowMapper opportunityRowMapper;
	private final DownloadOpportunityRowMapper downloadOpportunityRowMapper;
	private final OpportunityForEditRowMapper opportunityForEditRowMapper;
	private final OpportunityForStatusChangeRowMapper opportunityForStatusChangeRowMapper;
	private final ObjectMapper objectMapper;


	public OpportunityJDBCRepo(
			final JdbcTemplate jdbcTemplate,
			final NamedParameterJdbcTemplate parameterJdbcTemplate,
			final OpportunityRowMapper opportunityRowMapper,
			final DownloadOpportunityRowMapper downloadOpportunityRowMapper,
			final OpportunityForEditRowMapper opportunityForEditRowMapper,
			final OpportunityForStatusChangeRowMapper opportunityForStatusChangeRowMapper,
			final ObjectMapper objectMapper
	) {
		this.jdbcTemplate = jdbcTemplate;
		this.parameterJdbcTemplate = parameterJdbcTemplate;
		this.opportunityRowMapper = opportunityRowMapper;
		this.downloadOpportunityRowMapper = downloadOpportunityRowMapper;
		this.opportunityForEditRowMapper = opportunityForEditRowMapper;
		this.opportunityForStatusChangeRowMapper = opportunityForStatusChangeRowMapper;
		this.objectMapper = objectMapper;
	}

	private static StringBuilder addBusinessTypesToQuery(ScheduleRequest schedule, StringBuilder queryForOpps) {
		queryForOpps.append(" Where ");
		SQLQueryBuilder.addStartForInQuery(queryForOpps, "BusinessType");
		// TODO here is injection
		for (LineOfBusiness lineOfBusiness : schedule.getBusinessTypes()) {
			queryForOpps.append("'").append(String.join("','",
					lineOfBusiness.getPossibleLobAcordValues())).append("'");
			queryForOpps.append(",");
		}
		queryForOpps = SQLQueryBuilder.removeLastComma(queryForOpps);
		queryForOpps = SQLQueryBuilder.addEndingToInQuery(queryForOpps);
		return queryForOpps;
	}

	public boolean doesFirstTimestampExist(Integer oppId) {
		String sql = "SELECT COUNT (Opp.OpportunityId) FROM Opportunities Opp with (nolock) " +
				"WHERE timestampFirstCallPartner IS NOT NULL AND Opp.OpportunityId = " + oppId;
		return parameterJdbcTemplate.queryForObject(
				sql, new MapSqlParameterSource(OPPORTUNITY_ID_SQL_PARAM, oppId), Integer.class) > 0;
	}

	public void updateOpportunityForBLCall(Integer oppId, String time, String premiumValue, boolean doesFirstTimestampExist) {
		List<Object> paramsList = new LinkedList<>();
		String sql = buildQueryAndParamListForUpdateBL(oppId, time, premiumValue, doesFirstTimestampExist, paramsList);
		jdbcTemplate.update(sql, paramsList.toArray());
	}

	private String buildQueryAndParamListForUpdateBL(Integer oppId, String time, String premiumValue,
													 boolean doesFirstTimestampExist, List<Object> paramsList) {
		StringBuilder sql = new StringBuilder();
		sql.append("UPDATE Opportunities SET timestampCallPartner = ?");
		paramsList.add(time);
		if (!doesFirstTimestampExist) {
			sql.append(", timestampFirstCallPartner = ?");
			paramsList.add(time);
		}
		if (StringUtils.isNotBlank(premiumValue)) {
			sql.append(", lastQuotedPremium = ?");
			paramsList.add(premiumValue);
		}
		sql.append(WHERE_OPPORTUNITY_ID);
		paramsList.add(oppId);
		return sql.toString();
	}

	/**
	 * Get the opportunity data for the given opportunityid
	 *
	 * @return Opportunity object
	 */
	public Opportunity findByOpportunityId(int oppId) {
		String sb = SELECT +
				FROM_OPPORTUNITIES_OPP_WITH_NOLOCK +
				"where Opp.OpportunityId=:" +
				OPPORTUNITY_ID_SQL_PARAM;
		return queryOpportunity(sb, oppId);
	}

	private Opportunity queryOpportunity(String sql, int oppId) {
		return parameterJdbcTemplate.queryForObject(sql, new MapSqlParameterSource(OPPORTUNITY_ID_SQL_PARAM, oppId),
				opportunityRowMapper);
	}

	public List<Opportunity> getOppListByIdList(List<Integer> listOppIds) {
		return parameterJdbcTemplate.query(buildQueryForDownLoadOpportunities(),
				new MapSqlParameterSource(OPPORTUNITY_IDS_SQL_PARAM, listOppIds),
				downloadOpportunityRowMapper);
	}

	String buildQueryForDownLoadOpportunities() {
		return "SELECT Opp.lastPolicyGuid, " + "Opp.bookTransferID, " +
				"Opp.data, " +
				"Opp.customername, " +
				"Opp.effectivedate, " +
				"Opp.opportunityId, " +
				"Opp.lineType" +
				oppFromWhereIdInClause();
	}

	/**
	 * Finds all OppIds from the given schedule that needs to run
	 */
	//Map<LineType, List<Integer>>
	public Map<LineType, List<Integer>> getOppIdsForTheGivenSchedule(ScheduleRequest schedule) {
		log.info("Retrieve Schedule Opportunities for schedule - {}", schedule.getId());
		Map<LineType, List<Integer>> oppIds =  parameterJdbcTemplate.query(
				buildQueryFromTheGivenSchedule(schedule),
				new OpportunityForScheduleResultExtractor());
		log.info("Found {} to quote.",
				Objects.requireNonNull(oppIds, "parameterJdbcTemplate query returned null oppIds").size());
		log.info("Completed retrieving Schedule ");
		return oppIds;
	}

	/**
	 * update sql query where clause with the given effective date range
	 */
	protected StringBuilder
	addEffectiveDateCheckToQuery(StringBuilder sqlQuery, String startEffectiveDate, String endEffectiveDate) {
		// Add effectivedate check if dates passed
		if (isBothDatesPresent(startEffectiveDate, endEffectiveDate)) {
			sqlQuery.append(" And Opp.effectiveDate between '")
					.append(startEffectiveDate)
					.append("' and '")
					.append(endEffectiveDate)
					.append("'");
		} else if (StringUtils.isNotEmpty(startEffectiveDate)) {
			sqlQuery.append(" And Opp.effectiveDate >= '")
					.append(startEffectiveDate)
					.append("'");
		} else if (StringUtils.isNotEmpty(endEffectiveDate)) {
			sqlQuery.append(" And Opp.effectiveDate <= '")
					.append(endEffectiveDate)
					.append("'");
		}
		return sqlQuery;
	}

	protected boolean isBothDatesPresent(String startEffectiveDate, String endEffectiveDate) {
		return StringUtils.isNotEmpty(startEffectiveDate) && StringUtils.isNotEmpty(endEffectiveDate);
	}

	protected String buildQueryFromTheGivenSchedule(ScheduleRequest schedule) {
		StringBuilder queryForOpps = new StringBuilder("Select OpportunityID, Opp.LineType from Opportunities Opp ")
				.append("Join BookTransfer bt on Opp.BookTransferID = bt.BookTransferID");

		queryForOpps = buildWhereClauseForQuery(schedule, queryForOpps);

		return queryForOpps.toString();
	}

	/**
	 * Heritage, Issued, and Withdrawn statuses are not allowed to be quoted
	 */
	protected String getRestrictedStatuses() {
		return " And Opp.Status NOT in (" +
				OpportunityStatus.OPPORTUNITY_STATUS_HERITAGE.getOppStatusCode() + "," +
				OpportunityStatus.OPPORTUNITY_STATUS_ISSUED.getOppStatusCode() + "," +
				OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode() + ")";
	}

	private StringBuilder buildWhereClauseForStartEffectiveDate(ScheduleRequest schedule, StringBuilder queryForOpps){
		if(schedule.getStartEffectiveDate() != null){
			//PST to UTC conversion
			final Calendar cal = Calendar.getInstance();
			cal.setTime(schedule.getStartEffectiveDate());
			cal.add(Calendar.HOUR_OF_DAY, 6);
			queryForOpps.append(" And Opp.effectiveDate >= '")
					.append(DateUtils.getSQLDateString(cal.getTime()))
					.append("'");
		}
		return queryForOpps;
	}


	private StringBuilder buildWhereClauseForQuery(ScheduleRequest schedule, StringBuilder queryForOpps) {
		// TODO here is injection
		queryForOpps = addBusinessTypesToQuery(schedule, queryForOpps);

		queryForOpps = SQLQueryBuilder.buildQueryFromGivenRowNameAndPossibleValuesAnd(queryForOpps, "Opp.STATE",
				schedule.getRatingStates());
		queryForOpps = SQLQueryBuilder.buildQueryFromGivenRowNameAndPossibleValuesAnd(queryForOpps,
				"Opp.OppPriorCarrier", schedule.getPriorCarriers());
		queryForOpps = SQLQueryBuilder.buildQueryFromGivenRowNameAndPossibleValuesAnd(queryForOpps, "Opp.Status",
				schedule.readQuoteStatusKeys());

		queryForOpps.append(getRestrictedStatuses());

		buildWhereClauseWithBookTransfer(schedule, queryForOpps);

		buildWhereClauseForStartEffectiveDate(schedule, queryForOpps);

		queryForOpps = buildQueryForEffectiveDate(queryForOpps, schedule.getExportSLA());

		buildQueryExcludingManualQuotes(queryForOpps);


		return queryForOpps;
	}

	private StringBuilder buildQueryExcludingManualQuotes(final StringBuilder queryForOpps) {
		return queryForOpps.append(" AND QuoteType IS NULL");
	}

	private StringBuilder buildWhereClauseWithBookTransfer(ScheduleRequest schedule, StringBuilder queryForOpps) {
		// TODO here is injection
		queryForOpps.append(AND).append("(");
		StringBuilder innerQuery = new StringBuilder();

		innerQuery = SQLQueryBuilder.buildQueryWhereClauseWithMultipleRowValues(innerQuery, "bt.SalesforceCode",
				schedule.getSalesforceCodes());
		innerQuery = SQLQueryBuilder.addAndToInnerQueryIfNeeded(innerQuery, "active");
		innerQuery = SQLQueryBuilder.buildQueryFromGivenRowNameAndPossibleValue(innerQuery, "bt.status", "active");

		innerQuery = SQLQueryBuilder.addAndToInnerQueryIfNeeded(innerQuery, schedule.getStatCode());
		innerQuery = SQLQueryBuilder.buildQueryFromGivenRowNameAndPossibleValue(innerQuery, "bt.SubCode",
				schedule.getStatCode());

		String agentNum = Objects.toString(schedule.getMasterStatCode(), null);
		innerQuery = SQLQueryBuilder.addAndToInnerQueryIfNeeded(innerQuery, agentNum);

		innerQuery = SQLQueryBuilder.buildQueryFromGivenRowNameAndPossibleValue(innerQuery, "bt.AgentNum", agentNum);

		innerQuery = SQLQueryBuilder.addAndToInnerQueryIfNeeded(innerQuery, schedule.getNbdRelationship());
		innerQuery = SQLQueryBuilder.buildQueryFromGivenRowNameAndPossibleValue(innerQuery, "bt.NBDRelationship",
				schedule.getNbdRelationship());

		queryForOpps.append(innerQuery);
		queryForOpps.append(")");
		return queryForOpps;
	}

	private StringBuilder buildQueryForEffectiveDate(StringBuilder queryForOpps, Integer sla) {
		String futureDate = DateUtils.getDateXDaysInTheFuture(sla);
		String today = DateUtils.getDateXDaysInTheFuture(0);
		// TODO here is injection
		return queryForOpps
				.append(" AND EffectiveDate <= '")
				.append(futureDate)
				.append("' ")
				.append(" AND EffectiveDate >= '")
				.append(today)
				.append("' ");
	}

	/**
	 * From the custom filter request input We will get all the oppsIDs that would be returned
	 */
	public List<Integer> getCustomFiltersOppIds(CustomFilterRequestForOppIds cfRequest) {
		return parameterJdbcTemplate.queryForList(
				buildCustomFilterQueryForOppIds(cfRequest),
				prepareDataForCustomFiltersForOppIds(cfRequest),
				Integer.class);
	}

	private MapSqlParameterSource prepareDataForCustomFiltersForOppIds(CustomFilterRequestForOppIds cfRequest) {
		if (StringUtils.isNotBlank(cfRequest.getCustomerName())) {
			cfRequest.setCustomerName(buildRegexWithWildCardSpaces(cfRequest.getCustomerName()));
		}
		return new MapSqlParameterSource(objectMapper.convertValue(cfRequest, new TypeReference<>() {}));
	}

	private String buildRegexWithWildCardSpaces(String regexValue) {
		return "%" + regexValue.toLowerCase().replaceAll("\\s", "%") + "%";
	}

	public String buildCustomFilterQueryForOppIds(CustomFilterRequestForOppIds customFilterRequest) {
		return "SELECT Opp.OpportunityID " + buildBodyOfCustomFilterObjectForOppIds(customFilterRequest);
	}

	public String buildBodyOfCustomFilterObjectForOppIds(CustomFilterRequestForOppIds customFilterRequest) {
		StringBuilder sb = new StringBuilder(FROM_OPPORTUNITIES_OPP_WITH_NOLOCK);
		sb.append(", BookTransfer BT with (nolock) ");
		sb.append(", OpportunityStatus OppStatus with (nolock) ");
		sb.append("WHERE Opp.BookTransferID = BT.BookTransferID AND Opp.Status = OppStatus.ID");

		//Append Opp Status condition
		appendSearchCriteria(CollectionUtils.isNotEmpty(customFilterRequest.getStatuses()), sb,
				" AND Opp.Status in (:statuses)");

		//Append Subcode condition
		appendSearchCriteria(StringUtils.isNotBlank(customFilterRequest.getStatCode()), sb,
				" AND BT.SubCode = :statCode");

		//Append EffectiveDate greater than or equal condition
		appendSearchCriteria(StringUtils.isNotBlank(customFilterRequest.getStartDate()), sb,
				" AND Opp.EffectiveDate >= :startDate");

		//Append EffectiveDate less than or equal condition
		appendSearchCriteria(StringUtils.isNotBlank(customFilterRequest.getEndDate()), sb,
				" AND Opp.EffectiveDate <= :endDate");

		//Append Carrier condition
		appendSearchCriteria(CollectionUtils.isNotEmpty(customFilterRequest.getPriorCarriers()), sb,
				" AND BT.Carrier in (:priorCarriers)");

		//Append State condition
		appendSearchCriteria(CollectionUtils.isNotEmpty(customFilterRequest.getStates()), sb,
				" AND Opp.State in (:states)");

		//Append BusinessType condition
		appendSearchCriteria(CollectionUtils.isNotEmpty(customFilterRequest.getLobs()), sb,
				" AND ((Opp.BusinessType in (:lobs) AND (policytype is null or PolicyType = ''))");

		//append PolicyTypeCd condition for renters and condo
		appendSearchCriteria(CollectionUtils.isNotEmpty(customFilterRequest.getPolicyTypeCds()), sb,
				" OR (Opp.BusinessType = 'Home' AND Opp.PolicyType in (:policyTypeCds))");

		//Closing the BusinessType condition
		appendSearchCriteria(CollectionUtils.isNotEmpty(customFilterRequest.getLobs()), sb,
				")");

		//Append OpportunityId condition
		appendSearchCriteria(CollectionUtils.isNotEmpty(customFilterRequest.getOpportunityIDs()), sb,
				" AND Opp.OpportunityID in (:opportunityIDs)");

		//Append NNumber condition
		appendSearchCriteria(StringUtils.isNotBlank(customFilterRequest.getAssignedUser()), sb,
				" AND BT.Nnumber = :assignedUser");

		//Append UploadEventId condition
		appendSearchCriteria(customFilterRequest.getUploadEventId() != null
						&& customFilterRequest.getUploadEventId() != 0, sb,
				" AND Opp.UploadEventID = :" + UPLOAD_EVENT_ID_SQL_PARAM);

		// we check if blank here due to the fact if it is it will be looking for
		// everything with a space in it.
		//Append CustomerName condition
		appendSearchCriteria(StringUtils.isNotBlank(customFilterRequest.getCustomerName()), sb,
				" AND Opp.CustomerName LIKE :customerName");

		//Append NBDRelationShip condition
		appendSearchCriteria(CollectionUtils.isNotEmpty(customFilterRequest.getNbdRelationship()), sb,
				" AND BT.NBDRelationship in (:nbdRelationship)");

		//Append PriorPolicyGuid condition
		appendSearchCriteria(StringUtils.isNotEmpty(customFilterRequest.getPriorPolicyGuid()), sb,
				" AND Opp.PriorCarrierGuid = :priorPolicyGuid");

		//Append LastPolicyGuid condition
		appendSearchCriteria(StringUtils.isNotBlank(customFilterRequest.getSafecoPolicyGuid()), sb,
				" AND Opp.LastPolicyGuid = :" + LAST_POLICY_GUID_SQL_PARAM);

		//Append AgentNum condition
		appendSearchCriteria(StringUtils.isNotBlank(customFilterRequest.getAgentNumber()), sb,
				" AND BT.AgentNum = :agentNumber");

		//Append OppPriorCarrier condition
		appendSearchCriteria(CollectionUtils.isNotEmpty(customFilterRequest.getOppPriorCarriers()), sb,
				" AND Opp.OppPriorCarrier in (:oppPriorCarriers)");

		//Append SFDCID condition
		appendSearchCriteria(CollectionUtils.isNotEmpty(customFilterRequest.getSfdcids()), sb,
				" AND BT.SFDCID in (:sfdcids)");

		//Append BillingAccountNumber
		appendSearchCriteria(CollectionUtils.isNotEmpty(customFilterRequest.getPriorCarrierBillingAccountNumbers()), sb,
				" AND Opp.BillingAccountNumber in (:priorCarrierBillingAccountNumbers)");

		if (customFilterRequest.getLineType() != null) {
			String filterByLineType = getCheckByLineType(customFilterRequest.getLineType());
			if (!StringUtils.isEmpty(filterByLineType)) {
				sb.append(AND).append(filterByLineType);
			}
		}
		//Append NAICCD condition
		appendSearchCriteria(CollectionUtils.isNotEmpty(customFilterRequest.getNaicCds()), sb,
				" AND Opp.NAICCd in (:naicCds)");

		return sb.toString();
	}

	protected void appendSearchCriteria(boolean canAppend, StringBuilder sb, String searchCriteria) {
		if (canAppend) {
			sb.append(searchCriteria);
		}
	}

	protected String getCheckByLineType(LineType lineType) {
		if (LineType.Personal.equals(lineType) || LineType.Business.equals(lineType)) {
			return String.format("Opp.LineType = '%s'", lineType);
		}
		// agnostic LineType check for "All" or erroneous
		return "";
	}

	/**
	 * query for a list of opportunities to display from a custom filter request
	 */
	public List<CustomFilter> getCustomFilteredOpportunities(CustomFilterRequest cfRequest) {
		return parameterJdbcTemplate.query(buildCustomFilterQueryFromObject(cfRequest),
				cleanUpSearchIfSearchingForDate(cfRequest.getPaginationRequest()).buildParametersForPagination(),
				getCustomFilteredOpportunitiesMapper());

	}

	/**
	 * If we are searching we need to check if it for a date. If it is for a date which we are assuming is anything with
	 * two digits followed by a / Then we need to clean date search up ot match format expected by sql database.
	 */
	protected PaginationRequest cleanUpSearchIfSearchingForDate(PaginationRequest paginationRequest) {
		String search = paginationRequest.getSearch();
		if (search == null) {
			// do not need to update anything as no searching
			return paginationRequest;
		}
		// so in order for us to assume we are searching by date we need to have two
		// digits followed by a slash. Then the rest is optional
		Matcher matcher = Pattern.compile("(\\d{2})/(\\d{1,2})?/?(\\d{1,4})?").matcher(search);
		if (matcher.find()) {
			StringBuilder newDateSearch = new StringBuilder();
			String year = matcher.group(3);
			// we go all the way to year
			if (year != null) {
				if (year.length() < 4) {
					// add away to search for wildcard years
					// for example if they put in 05/06/2 then we need to search for 2%-05-06 to
					// match database search
					year += "%";
				}
				newDateSearch.append(year).append("-");
			}
			// it has to have the month so put that in
			String month = matcher.group(1);
			newDateSearch.append(month).append("-");

			String day = matcher.group(2);
			if (StringUtils.isNotBlank(day)) {
				newDateSearch.append(day);
			}

			paginationRequest.setSearch(newDateSearch.toString());
		}
		return paginationRequest;
	}

	/**
	 * query for a count of items that would be displayed from that pagination
	 */
	public Integer getCustomFilteredOpportunitiesCount(CustomFilterRequest cfRequest) {
		return parameterJdbcTemplate.queryForObject(buildCustomFilterQueryFromObjectForCount(cfRequest),
				cleanUpSearchIfSearchingForDate(cfRequest.getPaginationRequest()).buildParametersForPagination(),
				Integer.class);
	}

	public RowMapper<CustomFilter> getCustomFilteredOpportunitiesMapper() {
		return (rs, rownumber) -> {
			CustomFilter customFilter = new CustomFilter();
			customFilter.setOpportunityId(rs.getString("OpportunityID"));
			customFilter.setBookTransferId(rs.getString("bookTransferID"));
			customFilter.setStatus(rs.getString("Status"));
			customFilter.setSubCode(rs.getString("SubCode"));
			customFilter.setEffectiveDate(rs.getDate("EffectiveDate"));
			customFilter.setNAICCd(rs.getString("NAICCd"));
			customFilter.setSafecoPolicyGuid(rs.getString(LAST_POLICY_GUID_SQL_PARAM));
			customFilter.setPriorPremium(rs.getString("PriorPremium"));
			customFilter.setLastQuotedPremium(rs.getString("LastQuotedPremium"));
			customFilter.setLob(rs.getString("LOB"));
			customFilter.setCustomerName(rs.getString("CustomerName"));
			customFilter.setPriorCarrierGuid(rs.getString("PriorCarrierGuid"));
			customFilter.setMasterOppId(rs.getString("MasterOppID"));
			customFilter.setNNumber(rs.getString("Nnumber"));
			customFilter.setSalesforceCode(rs.getString("SalesforceCode"));
			customFilter.setState(rs.getString("State"));
			customFilter.setLineType(rs.getString("LineType"));
			customFilter.setNBDRelationship(rs.getString("NBDRelationship"));
			return customFilter;
		};
	}

	private String buildSelectClauseForCustomFilerObject() {
		return "SELECT Opp.OpportunityID, " +
				"Opp.bookTransferID, " +
				"OppStatus.Status, " +
				"BT.SubCode, " +
				"Opp.EffectiveDate, " +
				"Opp.LastPolicyGuid as safecoPolicyGuid, " +
				"Opp.NAICCd, " +
				"Opp.PriorPremium, " +
				"Opp.LastQuotedPremium, " +
				"Opp.BusinessType as LOB, " +
				"Opp.CustomerName, " +
				"Opp.PriorCarrierGuid, " +
				"Opp.State, " +
				"Opp.MasterOppID, " +
				"BT.Nnumber, " +
				"BT.SalesforceCode, " +
				"Opp.LineType, " +
				"BT.NBDRelationship ";
	}

	public String buildCustomFilterQueryFromObject(CustomFilterRequest customFilterRequest) {
		return buildSelectClauseForCustomFilerObject() +
				buildBodyOfCustomFilterObject(customFilterRequest) +
				handlePaginationForCustomFilters(customFilterRequest.getPaginationRequest());
	}

	public String buildCustomFilterQueryFromObjectForCount(CustomFilterRequest customFilterRequest) {
		return "Select Count(*) " +
				buildBodyOfCustomFilterObject(customFilterRequest) +
				handlePaginationForCustomFiltersCount(customFilterRequest.getPaginationRequest());
	}

	public String buildBodyOfCustomFilterObject(CustomFilterRequest customFilterRequest) {
		return FROM_OPPORTUNITIES_OPP_WITH_NOLOCK +
				"JOIN BookTransfer BT with (nolock) " +
				"ON Opp.BookTransferID = BT.BookTransferID " +
				"JOIN OpportunityStatus OppStatus with (nolock) " +
				"ON Opp.Status = OppStatus.ID " +
				"WHERE Opp.OpportunityID IN (" +
				StringUtils.join(customFilterRequest.getOppIds(), ",") +
				") "; // trailing space is important
	}

	private String handlePaginationForCustomFilters(PaginationRequest req) {
		return addAndIfItemNotBlank(req.getSearch()) +
				req.buildPaginationQuery(getColumnNamesForCustomFilters());
	}

	private String handlePaginationForCustomFiltersCount(PaginationRequest req) {
		return addAndIfItemNotBlank(req.getSearch()) +
				req.handleSearch(getColumnNamesForCustomFilters());
	}

	private String addAndIfItemNotBlank(String item) {
		if (StringUtils.isNotEmpty(item)) {
			return "AND ";
		}
		return "";
	}

	private String[] getColumnNamesForCustomFilters() {
		return new String[]{"Opp.OpportunityID", "OppStatus.Status", "BT.SubCode", "Opp.EffectiveDate", "Opp.NAICCd",
				"Opp.PriorPremium", "Opp.LastQuotedPremium", "Opp.BusinessType", "Opp.CustomerName", "Opp.State",
				"Opp.MasterOppID", "BT.Nnumber", "BT.SalesforceCode", "Opp.LineType"};
	}

	/**
	 * Get opportunity data and original xml for editing in AQE UI
	 *
	 * @return opportunity with Data and original xml
	 */
	public Opportunity findOpportunityForEdit(int oppId) {
		String sql = "SELECT Opp.OpportunityID, Opp.Data, Opp.OriginalXML, Opp.BookTransferID, Opp.NaicCd, Opp.LineType," +
				" Opp.BusinessType, Opp.BillingAccountNumber, Opp.State " +
				FROM_OPPORTUNITIES_OPP_WITH_NOLOCK +
				" WHERE Opp.OpportunityID=:" + OPPORTUNITY_ID_SQL_PARAM;
		return parameterJdbcTemplate.queryForObject(
				sql, new MapSqlParameterSource(OPPORTUNITY_ID_SQL_PARAM, oppId), opportunityForEditRowMapper);
	}

	/**
	 * get the distinct priorcarrier from opportunity table
	 *
	 * @return List of opp prior carriers
	 */
	public List<String> findAllDistinctOppPriorCarrier(LineType lineType) {
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT DISTINCT Opp.OppPriorCarrier FROM Opportunities Opp with (nolock)");
		String filterByLineType = getCheckByLineType(lineType);
		if (!StringUtils.isEmpty(filterByLineType)) {
			sb.append(" WHERE ").append(filterByLineType);
		}
		sb.append(" ORDER BY Opp.OppPriorCarrier ASC");

		return jdbcTemplate.queryForList(sb.toString(), String.class);
	}

	/**
	 * get the list of opportunities associated to a book
	 *
	 * @return list of opportunities
	 */
	public List<Opportunity> findByBookTransferID(int bookTransferID) {
		String sb = SELECT + FROM_OPPORTUNITIES_OPP_WITH_NOLOCK +
				"where Opp.BookTransferID=:" + BOOK_TRANSFER_ID_SQL_PARAM;

		return parameterJdbcTemplate.query(sb,
				new MapSqlParameterSource(BOOK_TRANSFER_ID_SQL_PARAM, bookTransferID),
				opportunityRowMapper);
	}

	/**
	 * get the distinct list of naiccd for the upload
	 *
	 * @return list of naiccd
	 */
	public List<String> getNAICCd(int uploadId, LineType lineType) {
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT DISTINCT Opp.NAICCd FROM Opportunities Opp  with (nolock) ");
		sb.append("WHERE Opp.NAICCd IS NOT NULL AND Opp.UploadEventID = :").append(UPLOAD_EVENT_ID_SQL_PARAM);
		String filterByLineType = getCheckByLineType(lineType);
		if (!StringUtils.isEmpty(filterByLineType)) {
			sb.append(AND).append(filterByLineType);
		}
		sb.append(" ORDER BY Opp.NAICCd ASC");

		return parameterJdbcTemplate.queryForList(sb.toString(),
				new MapSqlParameterSource(UPLOAD_EVENT_ID_SQL_PARAM, uploadId), String.class);
	}

	/**
	 * get the distinct list of states for the upload
	 *
	 * @return list of states
	 */
	public List<String> getStateList(int uploadId, LineType lineType) {
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT DISTINCT Opp.State FROM Opportunities Opp WITH (nolock) ");
		sb.append("WHERE Opp.State IS NOT NULL AND Opp.UploadEventID = :").append(UPLOAD_EVENT_ID_SQL_PARAM);
		String filterByLineType = getCheckByLineType(lineType);
		if (!StringUtils.isEmpty(filterByLineType)) {
			sb.append(AND).append(filterByLineType);
		}
		sb.append(" ORDER BY Opp.State ASC");

		return parameterJdbcTemplate.queryForList(sb.toString(),
				new MapSqlParameterSource(UPLOAD_EVENT_ID_SQL_PARAM, uploadId), String.class);
	}

	public List<Opportunity> findByOpportunityIdIn(List<Integer> opportunitiesIds) {
		return JPAHelper.partitionFindByXIn(new FindFromDatabaseInList() {
			@Override
			public <T> List<T> findFunction(Iterable iterable) {
				return findByOpportunityIdInExecutor(iterable);
			}
		}, opportunitiesIds);
	}

	/**
	 * get the list of opportunities with the given list of ids
	 *
	 * @return List of Opportunity
	 */
	public List<Opportunity> findByOpportunityIdInExecutor(Iterable<Integer> opportunitiesIds) {
		return parameterJdbcTemplate.query(
				buildQueryToFindOpportunitiesWithIds(),
				new MapSqlParameterSource(OPPORTUNITY_IDS_SQL_PARAM, opportunitiesIds),
				opportunityRowMapper);
	}

	protected String buildQueryToFindOpportunitiesWithIds() {
		return "SELECT *" + oppFromWhereIdInClause();
	}

	/**
	 * get the latest values of opportunities for the given list of ids to display in the AQE UI
	 *
	 * @return List of opportunities
	 */
	public List<OpportunityDisplay> reloadDataTable(List<Integer> listIds) {
		String select = "Select" +
				" Opp.opportunityId, Opp.status, Opp.effectiveDate, Opp.lastPolicyGuid, Opp.nAICCd, Opp.priorPremium, Opp.bookTransferID," +
				" Opp.lastQuotedPremium, Opp.businessType, Opp.customerName, Opp.priorCarrierGuid, Opp.state, Opp.masterOppID," +
				" bt.subCode, bt.carrier, bt.nnumber, bt.sFDCID, Opp.lineType" +
				" FROM Opportunities Opp with (nolock)" +
				" , BookTransfer bt with (nolock)" +
				" , OpportunityStatus oppStatus with (nolock)" +
				" WHERE bt.bookTransferID = Opp.bookTransferID AND Opp.status = oppStatus.ID AND Opp.opportunityId IN (:listIds)";
		Map<String, List<Integer>> paramMap = Collections.singletonMap("listIds", listIds);

		return parameterJdbcTemplate.query(select, paramMap, new BeanPropertyRowMapper<>(OpportunityDisplay.class));
	}

	/**
	 * update opportunity table with the data from qni
	 *
	 * @return updated status
	 */
	public boolean updateOpportunityWithQnIData(String dataXml, QuoteAndIssueUpdate qniData) {
		log.info("updated Q&I policyGuid - {}", qniData.getLastPolicyGuid());

		StringBuilder builder = new StringBuilder();
		builder.append("UPDATE Opportunities set Data = ? ");
		// TODO string injection
		if (qniData.getEffectiveDate() != null) {
			builder = SQLQueryBuilder.buildQueryFromGivenRowNameAndPossibleValue(builder, ", EffectiveDate",
					DateUtils.getSQLDateString(qniData.getEffectiveDate()));
		}
		builder = SQLQueryBuilder.buildQueryFromGivenRowNameAndPossibleValue(builder, ", CustomerName",
				qniData.getFirstName() + " " + qniData.getLastName());
		builder = SQLQueryBuilder.buildQueryFromGivenRowNameAndPossibleValue(builder, ", State",
				qniData.getRatingState());
		if (qniData.getLastQuotedPremium() != null) {
			builder = SQLQueryBuilder.buildQueryFromGivenRowNameAndPossibleValue(builder, ", LastQuotedPremium",
					String.valueOf(qniData.getLastQuotedPremium()));
		}
		if (qniData.getStatus() == OpportunityStatus.OPPORTUNITY_STATUS_ISSUED.getOppStatusCode()) {
			builder.append(", status=").append(qniData.getStatus());
			builder = SQLQueryBuilder.buildQueryFromGivenRowNameAndPossibleValue(builder, ", TimestampIssued",
					DateUtils.getCurrentDateString());
		}
		builder.append(" WHERE LastPolicyGuid = ?");

		Object[] updateParams = new Object[]{dataXml, qniData.getLastPolicyGuid()};

		int count = jdbcTemplate.update(builder.toString(), updateParams);
		if (count > 0) {
			return true;
		}
		log.info("Opportunity not updated with QnI data");
		return false;
	}

	public Opportunity updateOpportunityPostQuote(Opportunity opportunity) {
		String builder =
				"UPDATE Opportunities SET status=?, lastQuotedPremium=?, lastPolicyGuid=?" +
						", timestampCallPartner=?, timestampFirstCallPartner=?" +
						WHERE_OPPORTUNITY_ID;

		Object[] updateParams = new Object[]{
				opportunity.getStatus(),
				opportunity.getLastQuotedPremium(),
				opportunity.getLastPolicyGuid(),
				opportunity.getTimestampCallPartner(),
				opportunity.getTimestampFirstCallPartner(),
				opportunity.getOpportunityId()
		};

		int count = jdbcTemplate.update(builder, updateParams);
		if (count > 0) {
			return opportunity;
		}
		return null;
	}

	public Opportunity updateOpportunity(Opportunity opportunity) {
		String update = "UPDATE Opportunities set Data = ?, originalXML=?, status=?, effectiveDate=?, priorPremium=?, " +
				"lastQuotedPremium = ?, businessType=?, lastPolicyGuid=?, bookTransferID=?, uploadEventID=?, " +
				"nAICCd = ?, customerName=?, priorCarrierGuid=?, state=?, oppPriorCarrier=?, agencyId=?, masterOppID=?" +
				WHERE_OPPORTUNITY_ID;

		Object[] updateParams = new Object[]{opportunity.getData(), opportunity.getOriginalXML(),
				opportunity.getStatus(), opportunity.getEffectiveDate(), opportunity.getPriorPremium(),
				opportunity.getLastQuotedPremium(), opportunity.getBusinessType(), opportunity.getLastPolicyGuid(),
				opportunity.getBookTransferID(), opportunity.getUploadEventID(), opportunity.getNAICCd(),
				opportunity.getCustomerName(), opportunity.getPriorCarrierGuid(), opportunity.getState(),
				opportunity.getOppPriorCarrier(), opportunity.getAgencyId(), opportunity.getMasterOppID(), opportunity.getOpportunityId()};

		int count = jdbcTemplate.update(update, updateParams);
		if (count > 0) {
			return opportunity;
		}
		return null;
	}

	public String getDataXmlStringByLastPolicyGuid(final String lastPolicyGuid) {
		log.info("Retrieving data xml as string for lastPolicyGuid {}", lastPolicyGuid);

		StringBuilder sb = new StringBuilder();
		sb.append("SELECT Opp.data FROM Opportunities Opp with (nolock) ");
		sb.append("WHERE LastPolicyGuid = :");
		sb.append(LAST_POLICY_GUID_SQL_PARAM);

		try {
			final String dataXmlString = parameterJdbcTemplate.queryForObject(sb.toString(),
					new MapSqlParameterSource(LAST_POLICY_GUID_SQL_PARAM, lastPolicyGuid), String.class);
			log.info("Finished retrieving data xml as string for lastPolicyGuid {}; Data {} blank.", lastPolicyGuid,
					StringUtils.isBlank(dataXmlString) ? "is" : "is not");
			return dataXmlString;
		} catch (final EmptyResultDataAccessException erdae) {
			log.info("Opportunity with LastPolicyGuid {} does not exist; Returning null", lastPolicyGuid, erdae);
		} catch (final Exception e) {
			log.error("Unexpected exception occurred while trying to retrieve data for opportunity with LastPolicyGuid {}",
					lastPolicyGuid, e);
		}
		return null;
	}

	/**
	 * Get the min and max effective date from the filtered opportunities
	 */
	public EffectiveDateRange getCustomFilterOpportunitiesEffectiveDateRange(List<Integer> oppIds) {
		EffectiveDateRange ret = new EffectiveDateRange();

		for (List<Integer> part : ListUtils.partition(oppIds, SQL_BATCH_SIZE)) {
			EffectiveDateRange result = parameterJdbcTemplate
					.queryForObject(buildCustomFilterEffectiveDateRangerQueryFromObject(),
							new MapSqlParameterSource(OPPORTUNITY_IDS_SQL_PARAM, part),
							new BeanPropertyRowMapper<>(EffectiveDateRange.class));

			if (result == null) { // shouldn't happen but ide complains about null safety
				continue;
			}

			if (ret.getMaxEffectiveDate() == null) {
				// first partition
				ret.setMaxEffectiveDate(result.getMaxEffectiveDate());
				ret.setMinEffectiveDate(result.getMinEffectiveDate());
			} else {
				// 2nd and subsequent partitions
				ret.setMaxEffectiveDate(maxDate(ret.getMaxEffectiveDate(), result.getMaxEffectiveDate()));
				ret.setMinEffectiveDate(minDate(ret.getMinEffectiveDate(), result.getMinEffectiveDate()));
			}
		}

		return ret;
	}

	private String buildCustomFilterEffectiveDateRangerQueryFromObject() {
		return "Select MIN(effectivedate) as minEffectiveDate, MAX(effectivedate) as maxeffectiveDate " +
				oppFromWhereIdInClause();
	}

	public Map<Integer, Integer> getBookIds(List<Integer> opportunityIds) {
		return parameterJdbcTemplate.query(buildQueryToFindBookIdsWithOppIds(),
				new MapSqlParameterSource(OPPORTUNITY_IDS_SQL_PARAM, opportunityIds), extractResultsForBookId());
	}

	private ResultSetExtractor<Map<Integer, Integer>> extractResultsForBookId() {
		return rs -> {
			HashMap<Integer, Integer> mapRet = new HashMap<>();
			while (rs.next()) {
				mapRet.put(rs.getInt(OPPORTUNITY_ID_SQL_PARAM), rs.getInt(BOOK_TRANSFER_ID_SQL_PARAM));
			}
			return mapRet;
		};
	}

	protected String buildQueryToFindBookIdsWithOppIds() {
		return "SELECT " + OPPORTUNITY_ID_SQL_PARAM + "," + BOOK_TRANSFER_ID_SQL_PARAM + oppFromWhereIdInClause();
	}

	private String oppFromWhereIdInClause() {
		return " FROM Opportunities Opp WITH (nolock) WHERE OpportunityID IN (:" + OPPORTUNITY_IDS_SQL_PARAM + ")";
	}

	public List<OpportunityDetails> getOppDetails(
			Set<Integer> bookTransferIds,
			List<String> lobs,
			String startEffectiveDate,
			String endEffectiveDate,
			LineType lineType
	) {
		StringBuilder sqlQuery = new StringBuilder()
				.append("SELECT OpportunityId,BooktransferId,BusinessType,EffectiveDate,LineType ")
				.append("FROM Opportunities Opp WITH (nolock) WHERE BookTransferId IN (:booktransferIds)");

		MapSqlParameterSource paramMapper = new MapSqlParameterSource("booktransferIds", bookTransferIds);

		if (!lobs.isEmpty()) {
			sqlQuery.append(" AND Opp.businessType in ( :lobs )");
			paramMapper.addValue("lobs", lobs);
		}

		// Add effective date check if dates passed
		sqlQuery = addEffectiveDateCheckToQuery(sqlQuery, startEffectiveDate, endEffectiveDate);

		String filterByLineType = getCheckByLineType(lineType);
		if (!StringUtils.isEmpty(filterByLineType)) {
			sqlQuery.append(AND).append(filterByLineType);
		}

		return parameterJdbcTemplate.query(sqlQuery.toString(), paramMapper, new OpportunityDetailsMapper());
	}

	public List<OpportunityErrorInfo> getErrorItemsByOppIds(List<Integer> opportunityIds) {
		MapSqlParameterSource paramMapper = new MapSqlParameterSource("opportunityIds", opportunityIds);
		String sqlQuery = "SELECT BookTransferId, AgencyId, OpportunityId, State, BusinessType, TimeStampCallPartner, LineType " +
				"FROM Opportunities Opp with (nolock) WHERE OpportunityId in (:opportunityIds)";
		return parameterJdbcTemplate.query(sqlQuery, paramMapper, new OpportunityErrorInfoMapper());
	}

	public List<OpportunityXmlData> getDataXmlForOppIds(Set<Integer> opportunityIds) {
		MapSqlParameterSource paramMapper = new MapSqlParameterSource("opportunityIds", opportunityIds);
		String sqlQuery = "SELECT Data, OpportunityId, BookTransferId, LineType " +
				"FROM Opportunities Opp with (nolock) WHERE OpportunityId in (:opportunityIds)";
		return parameterJdbcTemplate.query(sqlQuery, paramMapper, new OpportunityXmlDataMapper());
	}

	// TODO: error handling for multiple opps with this guid found?
	public Opportunity findOpportunityByLastPolicyGuid(String lastPolicyGuid) {
		String select = SELECT +
				FROM_OPPORTUNITIES_OPP_WITH_NOLOCK +
				"WHERE LastPolicyGuid = :" +
				LAST_POLICY_GUID_SQL_PARAM;

		return parameterJdbcTemplate.queryForObject(select,
				new MapSqlParameterSource(LAST_POLICY_GUID_SQL_PARAM, lastPolicyGuid), new OpportunityRowMapper());
	}

	@VisibleForTesting
	static String maxDate(String d1, String d2) {
		if (LocalDate.parse(d1).isAfter(LocalDate.parse(d2))) {
			return d1;
		}
		return d2;
	}

	@VisibleForTesting
	static String minDate(String d1, String d2) {
		if (LocalDate.parse(d1).isBefore(LocalDate.parse(d2))) {
			return d1;
		}
		return d2;
	}

	/**
	 * Finds opportunities by customer matching criteria for duplicate detection.
	 * Matches on prior policy number, line of business, and book ID.
	 *
	 * @param priorCarrierGuid The prior policy number
	 * @param businessType The line of business
	 * @param bookTransferID The book transfer ID
	 * @return List of opportunities matching the criteria
	 */
	public List<Opportunity> findOpportunitiesByCustomerCriteria(String priorCarrierGuid, String businessType, Integer bookTransferID) {
		String sql = SELECT + FROM_OPPORTUNITIES_OPP_WITH_NOLOCK +
				"WHERE Opp.PriorCarrierGuid = :priorCarrierGuid " +
				"AND Opp.BusinessType = :businessType " +
				"AND Opp.BookTransferID = :bookTransferID " +
				"ORDER BY Opp.OpportunityId DESC"; // Latest first

		MapSqlParameterSource params = new MapSqlParameterSource()
				.addValue("priorCarrierGuid", priorCarrierGuid)
				.addValue("businessType", businessType)
				.addValue("bookTransferID", bookTransferID);

		return parameterJdbcTemplate.query(sql, params, opportunityRowMapper);
	}
}
