package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.Domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerAccountAqe;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerAccountRequest;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerAccountServiceResponseData;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerAccountServiceResponseWrapper;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerMetadata;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerPolicy;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.LibertyPolicy;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.PriorPolicy;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.SearchCriteria;
import com.lmig.uscm.booktransfer.opportunity.services.utility.OpportunityUtil;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.BusinessAcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.domain.TransactionStatus;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.EndTransactionRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.MetaInfoRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.StartTransactionRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.request.TransactionEventRequest;
import com.lmig.usconsumermarkets.booktransfer.auditlogrestapi.client.response.TransactionResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatusCode;
import org.springframework.web.reactive.function.client.WebClient;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;
import reactor.core.publisher.Mono;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.xpath.XPathExpressionException;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class CustomerAccountHelper {
  public static final String CUSTOMER_ACCOUNT_SERVICE_ERROR = "error";
  public static final String ORIGIN = "origin";
  public static final String OPPORTUNITY_SERVICE = "Opportunity-Service";
  public static final String CREATE_UPDATE_CUSTOMER_ACCOUNT =  "CREATE_UPDATE_CUSTOMER_ACCOUNT";
  private final WebClient customerAccountWebClient;
  private final String url;
  private  final AuditLogHelper auditLogHelper;
  private final DuplicateOpportunityService duplicateOpportunityService;

  public CustomerAccountHelper(WebClient customerAccountWebClient, String url, AuditLogHelper auditLogHelper, DuplicateOpportunityService duplicateOpportunityService) {
    this.customerAccountWebClient = customerAccountWebClient;
    this.url = url;
    this.auditLogHelper = auditLogHelper;
    this.duplicateOpportunityService = duplicateOpportunityService;
  }

  public String getEcliqAccountNumber(String queryParams) {
    String uri = String.format("%s/ecliqAccountNumber?%s", this.url, queryParams);
    log.info("GET - {}", uri);

    return customerAccountWebClient
            .get()
            .uri(uri)
            .retrieve()
            .bodyToMono(String.class)
            .block();
  }

  public CustomerAccountServiceResponseData createCustomerAccount(Opportunity opp, Document workingDoc, Integer sFDCID)
          throws XPathExpressionException {
    log.info("Initiating audit Log transaction");

    TransactionResponse transactionResponses = OpportunityUtil.runFunctionWithRetriesWithDefault(
            auditLogHelper::startTransaction, startTransactionRequest(List.of(opp)), null);
    CustomerAccountRequest request = buildCustomerAccountRequest(opp, workingDoc, sFDCID, null, Optional.ofNullable(transactionResponses).map(TransactionResponse::getId).orElse(null));

    CustomerAccountServiceResponseWrapper responseWrapper;
    try {
      responseWrapper = sendCreateCustomerAccountRequest(request);
    } catch (Exception e) {
        log.error("Error creating customer account", e);
        return null;
    }

    log.debug("Customer Account Service response: {}", responseWrapper);

    if (StringUtils.isEmpty(responseWrapper.getError())) {
      return Objects.requireNonNull(
              responseWrapper,
              "Received null CustomerAccountServiceResponseWrapper from customer account service /policy"
      ).getData().get(0);
    }
    return null;
  }

  private static CustomerAccountRequest buildCustomerAccountRequest(Opportunity opp, Document workingDoc, Integer sFDCID, Integer currentSFDCID, String transactionId) throws XPathExpressionException {
    String priorPolicyNumber = BusinessAcordHelper.getPriorPolicyNumber(workingDoc);

    CustomerMetadata customerMetadata = CustomerMetadata.builder()
            .btCode(sFDCID.toString())
            .customerName(opp.getCustomerName())
            .address1(AcordHelper.getInsuredMailingAddr1(workingDoc))
            .city(AcordHelper.getInsuredMailingCity(workingDoc))
            .state(AcordHelper.getInsuredMailingState(workingDoc))
            .zip(AcordHelper.getInsuredMailingPostalCode(workingDoc))
            .build();

    String customerAccountId = AcordHelper.getCustomerAccountId(workingDoc);
    SearchCriteria.SearchCriteriaBuilder searchCriteria = SearchCriteria.builder()
            .customerAccountId(customerAccountId.equals(CustomerAccountHelper.CUSTOMER_ACCOUNT_SERVICE_ERROR)
              || customerAccountId.isEmpty()
              ? null : customerAccountId)
            .priorCarrierPolicyNumber(priorPolicyNumber)
            .btCode(currentSFDCID!= null ?currentSFDCID.toString():sFDCID.toString())
            .customerMetadata(customerMetadata);

    String customerPolicyId = AcordHelper.getCustomerPolicyId(workingDoc);
    //Opp status description
    String oppStatus = null;
    try {
      oppStatus = OpportunityStatus.getOppStatusFromCode(opp.getStatus()).getOppStatusValue();
    } catch (Exception e) {
      oppStatus = String.valueOf(opp.getStatus());
    }
    CustomerPolicy customerPolicy = CustomerPolicy.builder()
            ._id(customerPolicyId.equals(CustomerAccountHelper.CUSTOMER_ACCOUNT_SERVICE_ERROR)
              || customerPolicyId.isEmpty()
              ? null : customerPolicyId)
            .btCode(sFDCID)
            .emailAddress(BusinessAcordHelper.getInsuredEmailAddress(workingDoc))
            .phoneNumber(BusinessAcordHelper.getInsuredPhoneNumber(workingDoc))
            .ratingState(BusinessAcordHelper.getRatingState(workingDoc))
            .priorPolicy(PriorPolicy.builder()
                    .lineOfBusiness(opp.getBusinessType())
                    .carrier(opp.getOppPriorCarrier())
                    .policyNumber(opp.getPriorCarrierGuid())
                    .build())
            .libertyPolicy(LibertyPolicy.builder()
              .effectiveDate(opp.getEffectiveDate())
              .build())
            .aqe(CustomerAccountAqe.builder()
                    .opportunityId(opp.getOpportunityId())
                    .priorPremium(opp.getPriorPremium())
                    .priorExpirationDate(opp.getEffectiveDate())
                    .status(oppStatus)
                    .businessType(opp.getBusinessType())
                    .nNumber(opp.getNNumber())
                    .build())
            .build();

    return CustomerAccountRequest.builder()
            .searchCriteria(searchCriteria.build())
            .customerMetadata(customerMetadata)
            .customerPolicies(List.of(customerPolicy))
            .eventSource("AQE")
            .transactionId(transactionId)
            .build();
  }

  protected CustomerAccountServiceResponseWrapper sendCreateCustomerAccountRequest(CustomerAccountRequest request) {
    return sendCreateCustomerAccountRequests(List.of(request));
}

  protected CustomerAccountServiceResponseWrapper sendCreateCustomerAccountRequests(List<CustomerAccountRequest> requests) {


    log.debug("Full Customer Account Service request payload: {}", requests);

    CustomerAccountServiceResponseWrapper responseWrapper = OpportunityUtil.runFunctionWithRetriesWithDefault(
      req -> {

        CustomerAccountServiceResponseWrapper response = customerAccountWebClient
          .post()
          .uri(this.url + "/policy")
          .bodyValue(req)
          .retrieve()
          .onStatus(HttpStatusCode::is4xxClientError, clientResponse -> {
            log.error("=== 4XX CLIENT ERROR RECEIVED ===");
            log.error("Status Code: {}", clientResponse.statusCode().value());
            log.error("Headers: {}", clientResponse.headers().asHttpHeaders());
            return clientResponse.bodyToMono(String.class)
                    .flatMap(errorBody -> {
                      log.error("4XX Error Response Body: {}", errorBody);
                      log.error("=== END 4XX ERROR ===");
                      return Mono.empty();
                    });
          })
          .onStatus(status -> status.value() == 502, clientResponse -> {
            log.warn("=== 502 BAD GATEWAY RECEIVED ===");
            log.warn("Headers: {}", clientResponse.headers().asHttpHeaders());
            log.warn("Received 502 status code, retrying...");
            return Mono.error(new RuntimeException("Received 502 status code for sendCreateCustomerAccountRequest"));
          })
          .onStatus(HttpStatusCode::is5xxServerError, clientResponse -> {
            log.error("=== 5XX SERVER ERROR RECEIVED ===");
            log.error("Status Code: {}", clientResponse.statusCode().value());
            log.error("Headers: {}", clientResponse.headers().asHttpHeaders());
            return clientResponse.bodyToMono(String.class)
              .flatMap(errorBody -> {
                log.error("5XX Error Response Body: {}", errorBody);
                log.error("=== END 5XX ERROR ===");
                return Mono.empty();
              });
          })
          .bodyToMono(CustomerAccountServiceResponseWrapper.class)
          .block();


        return response;
      },
      requests,
      null
    );



    if(responseWrapper == null) {
      log.error("Error creating customer account - Customer Account Service response is null");
      auditLogHelper.endTransactionEvent(
        requests.get(0).getTransactionId(),
        EndTransactionRequest.builder()
          .status(TransactionStatus.FAILED)
          .metaInfo(MetaInfoRequest.builder()
            .eventMetaInfo(Map.of(ORIGIN, OPPORTUNITY_SERVICE))
            .build())
          .build()
      );
    }
    return responseWrapper;
  }

  public CustomerAccountServiceResponseWrapper updateCustomerAccounts(List<Opportunity> opportunities, Map<Integer, Integer> bookTransferIdToSfdcid, String newSFDCID)  {
    // build request
    List<CustomerAccountRequest> requests = new ArrayList<>();

    TransactionResponse transactionResponses = OpportunityUtil.runFunctionWithRetriesWithDefault(
            auditLogHelper::startTransaction,
            startTransactionRequest(opportunities),
            null
    );

    String transactionId = Optional.ofNullable(transactionResponses).map(TransactionResponse::getId).orElse(null);

    for(Opportunity opportunity : opportunities) {
      Integer sFDCID = null;
      Integer currentSFDCID = null;
      if(StringUtils.isBlank(newSFDCID)) {
        sFDCID = bookTransferIdToSfdcid.get(opportunity.getBookTransferID());
      }else{
        sFDCID = Integer.parseInt(newSFDCID);
        currentSFDCID = bookTransferIdToSfdcid.get(opportunity.getBookTransferID());
      }
      if (sFDCID == null) {
        log.error("SFDCID not found for book transfer id: {}", opportunity.getBookTransferID());
        continue;
      }

      // Apply stop-gap logic: Use the appropriate opportunity for customer policy update
      Opportunity opportunityForUpdate = duplicateOpportunityService.getOpportunityForCustomerPolicyUpdate(opportunity);

      if (opportunity.getOpportunityId() != opportunityForUpdate.getOpportunityId()) {
        log.info("Using opportunity {} instead of {} for customer policy update",
                opportunityForUpdate.getOpportunityId(), opportunity.getOpportunityId());
      }

      try {
        Document xmlDoc = XmlHelper.getDocument(opportunityForUpdate.getData());
        CustomerAccountRequest request = buildCustomerAccountRequest(opportunityForUpdate, xmlDoc, sFDCID, currentSFDCID, transactionId);
        requests.add(request);
      } catch (ParserConfigurationException | IOException | SAXException | XPathExpressionException e) {
        log.error("Failed to build customer account request for opportunity: {}", opportunityForUpdate.getOpportunityId(), e);
      }
    }

    CustomerAccountServiceResponseWrapper response = sendCreateCustomerAccountRequests(requests);

    return response;
  }

  private StartTransactionRequest startTransactionRequest(List<Opportunity> opportunities) {

    return StartTransactionRequest.builder()
            .type(CustomerAccountHelper.CREATE_UPDATE_CUSTOMER_ACCOUNT).events(List.of(
                    TransactionEventRequest.builder()
                            .name("CUSTOMER_ACCOUNTS_REQUEST")
                            .status(TransactionStatus.INITIATED)
                            .eventTimeStamp(LocalDateTime.now())
                            .endTimeStamp(LocalDateTime.now())
                            .metaInfo(MetaInfoRequest.builder().eventMetaInfo(Map.of(ORIGIN, OPPORTUNITY_SERVICE)).build()).build()))
            .metaInfo(MetaInfoRequest.builder()
                    .transactionMetaInfo(Map.of("opportunities", opportunities.stream().
                            map(Opportunity::getOpportunityId).collect(Collectors.toList()),
                            ORIGIN, OPPORTUNITY_SERVICE))
                    .build()).build();
  }
}
