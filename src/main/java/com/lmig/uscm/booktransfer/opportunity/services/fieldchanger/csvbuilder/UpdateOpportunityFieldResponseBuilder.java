package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.csvbuilder;

import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.utilityservice.CSVBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * This class can take opportunity results and build a spread sheet that can be
 * generated to the user with the sucess or failure or updating opportunity or
 * quote report item
 */
@Slf4j
public abstract class UpdateOpportunityFieldResponseBuilder {
	public static final String SUCCESS = "Success";

	protected CSVBuilder successOpps = new OppCSVBuilder();
	protected int successOppsCount = 0;
	protected CSVBuilder failedOpps = new OppCSVBuilder();
	protected CSVBuilder successQuoteReports = new OppCSVBuilder();
	protected int successQuoteReportItemsCount = 0;
	protected CSVBuilder failedQuoteReports = new OppCSVBuilder();

	public abstract String buildOutStatusChangeReport();

	protected void buildSummarySection(CSVBuilder csvSheet) {
		csvSheet.addRowToCSV(false, "Total number of opportunities successfully updated",
				String.valueOf(successOppsCount));

		csvSheet.addRowToCSV(false, "Total number of quote report items successfully updated",
				String.valueOf(successQuoteReportItemsCount));
	}

	protected void buildOpportunitySection(CSVBuilder csvSheet) {
		getOpportunityHeaders(csvSheet);
		csvSheet.addRowToCSV(failedOpps);
		csvSheet.addRowToCSV(successOpps);
	}

	protected abstract CSVBuilder getOpportunityHeaders(CSVBuilder csvSheet);

	protected abstract List<String> addQRIDataToCSV(OppChangeFieldResult oppChangeFieldResult);

	protected abstract List<String> addOppDataToRow(OppChangeFieldResult oppChangeFieldResult);

	public void addOppChangeFieldResults(List<OppChangeFieldResult> results) {
		for (OppChangeFieldResult result : results) {
			addOppChangeFieldResult(result);
		}
	}

	public void addOppChangeFieldResult(OppChangeFieldResult result) {
		if (result.didFail()) {
			log.info("addOppChangeFieldResultdidFail -  startDate {}", result.getFieldUpdatedValue());
			this.addFailedOpp(result);
			this.addFailedQuoteReportItem(result);
		} else {
			log.info("addOppChangeFieldResultdidPass -  startDate {}", result.getFieldUpdatedValue());
			this.addSuccessOpp(result);
			this.addSuccessQuoteReportItem(result);
		}
	}

	private void addSuccessOpp(OppChangeFieldResult oppChangeFieldResult) {
		successOpps.addRowToCSV(addOppDataToRow(oppChangeFieldResult));
		successOppsCount++;
	}

	private void addFailedOpp(OppChangeFieldResult oppChangeFieldResult) {
		failedOpps.addRowToCSV(addOppDataToRow(oppChangeFieldResult));
	}

	private void addSuccessQuoteReportItem(OppChangeFieldResult oppChangeFieldResult) {
		successQuoteReports.addRowToCSV(addQRIDataToCSV(oppChangeFieldResult));
		successQuoteReportItemsCount++;
	}

	private void addFailedQuoteReportItem(OppChangeFieldResult oppChangeFieldResult) {
		failedQuoteReports.addRowToCSV(addQRIDataToCSV(oppChangeFieldResult));
	}

	/**
	 * Sees if object was updated. It was updated if it did not fail and updated
	 * value is differetn that init value
	 *
	 * @param oppChangeFieldResult
	 * @return
	 */
	protected String getChangeValue(OppChangeFieldResult oppChangeFieldResult) {
		if (oppChangeFieldResult.didFail()
				|| oppChangeFieldResult.getOriginalValue().equals(oppChangeFieldResult.getFieldUpdatedValue())) {
			return "NO";
		}
		return "YES";
	}

}
