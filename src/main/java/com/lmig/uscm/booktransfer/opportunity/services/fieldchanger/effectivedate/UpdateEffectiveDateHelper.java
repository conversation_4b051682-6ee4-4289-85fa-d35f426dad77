/*
 * Copyright (C) 2017, Liberty Mutual Group
 *
 * Created on Mar 29, 2017
 */

package com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferException;
import com.lmig.uscm.booktransfer.email.client.domain.EmailException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.ChangeEffectiveDateResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.MassUpdaterForChangeEffectiveDate;
import com.lmig.uscm.booktransfer.opportunity.domain.fieldchanger.OppChangeFieldResult;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.ExportHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.csvbuilder.UpdateEffectiveDateResponseBuilder;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.csvbuilder.UpdateOpportunityFieldResponseBuilder;
import com.lmig.uscm.booktransfer.quotereport.client.domain.QuoteReportItemLegacy;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.BusinessAcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.w3c.dom.Document;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * This is hte base class for updating effective date
 */

@Slf4j
public abstract class UpdateEffectiveDateHelper {

	protected final ExportHelper exportHelper;
	private final OpportunityHelper opportunityHelper;
	private final OpportunityRepoHelper opportunityRepoHelper;

	public UpdateEffectiveDateHelper(final OpportunityHelper opportunityHelper, final ExportHelper exportHelper, final OpportunityRepoHelper opportunityRepoHelper) {
		this.opportunityHelper = opportunityHelper;
		this.exportHelper = exportHelper;
		this.opportunityRepoHelper = opportunityRepoHelper;
	}

	protected abstract ChangeEffectiveDateResponse
	sendResultCSVs(List<Opportunity> opps, String resultsCSV, String userEmail)
			throws JsonProcessingException, BookTransferException, EmailException;

	protected abstract List<OppChangeFieldResult> updateEffectiveDatesByPackage(
			List<OppChangeFieldResult> packagedOpportunities,
			LocalDate bookTransferStartDate);

	protected abstract MassUpdaterForChangeEffectiveDate
	buildInitialOpporytunityResults(List<Opportunity> opportunities);

	/**
	 * update effectivedate of the opportunity It will go through and match items on agencyid (this seems to be the old
	 * customer id at an old agency so that way home and auto policies will be joined) It will then generate a
	 * spreadsheet that shows what has been completed It will then also generate a spread sheet that will show items
	 * that could be matches that matched on the first 6 chars if we are home or true lead where they try to match
	 * customers. It will then send out an email out that handles this.
	 *
	 * @return both type of csv for partial matches and full matches. (partial matches could be null)
	 */
	public ChangeEffectiveDateResponse
	setLeadEffectiveDate(List<Integer> opportunityIds, String bookTransferStartDate, String usersEmail)
			throws JsonProcessingException, BookTransferException, EmailException {
		LocalDate startDate = getParsedStartDate(bookTransferStartDate);
		log.info("setLeadEffectiveDate -  startDate {}", startDate);

		List<Opportunity> opps = opportunityRepoHelper.findOpportunitiesByIds(opportunityIds);

		String resultsCSV = generateCSVForUpdateEffectiveDate(updateEffectiveDate(opps, startDate));

		return sendResultCSVs(opps, resultsCSV, usersEmail);
	}

	private LocalDate getParsedStartDate(String dateString) {
		try {
			return LocalDate.parse(dateString, DateTimeFormatter.ofPattern("MM/dd/yyyy"));
		} catch (DateTimeParseException e) {
			throw new IllegalArgumentException("Invalid Date passed to user", e);
		}
	}

	private String generateCSVForUpdateEffectiveDate(MassUpdaterForChangeEffectiveDate results) {
		UpdateOpportunityFieldResponseBuilder csvBuilder = new UpdateEffectiveDateResponseBuilder();
		log.info("generateCSVForUpdateEffectiveDate -  fieldName {} fieldName {}", results.getAllOpportunityResults().get(0).getFieldName(), 	results.getAllOpportunityResults().get(0).getFieldUpdatedValue());

		csvBuilder.addOppChangeFieldResults(results.getAllOpportunityResults());
		return csvBuilder.buildOutStatusChangeReport();
	}

	private MassUpdaterForChangeEffectiveDate updateEffectiveDate(List<Opportunity> packagedOpportunities,
																  LocalDate bookTransferStartDate) {
		// go through each customer package and update the opps.
		MassUpdaterForChangeEffectiveDate matchingCustomersMap = buildInitialOpporytunityResults(packagedOpportunities);
		for (String key : matchingCustomersMap.getAllCustomPackagePairingKey()) {
			updateEffectiveDatesByPackage(matchingCustomersMap.getAllNotFailedOpportunityResults(key),
					bookTransferStartDate);
		}
		updateDBForLeadEffectiveDate(matchingCustomersMap);
		return matchingCustomersMap;
	}

	private void updateDBForLeadEffectiveDate(MassUpdaterForChangeEffectiveDate results) {
		List<Opportunity> opportunities = new ArrayList<>();
		List<Opportunity> commercialOpportunities = new ArrayList<>();
		List<QuoteReportItemLegacy> quoteReports = new ArrayList<>();
		for (OppChangeFieldResult result : results.getAllOpportunityResults()) {
			if (!result.didFail()) {
				if (LineType.Personal.equals(result.getOpportunity().getLineType())) {
					QuoteReportItemLegacy quoteReportItem = new QuoteReportItemLegacy();
					quoteReportItem.setQuoteSalesforceID(String.valueOf(result.getOpportunity().getOpportunityId()));
					quoteReportItem.setEffectiveDate(result.getOpportunity().getEffectiveDate());
					quoteReports.add(quoteReportItem);
					opportunities.add(result.getOpportunity());
				} else {
					commercialOpportunities.add(result.getOpportunity());
				}
			}
		}
		for (List<QuoteReportItemLegacy> qriPartition : ListUtils.partition(quoteReports, OpportunityJDBCRepo.SQL_BATCH_SIZE)) {
			opportunityHelper.updateQRIForLeadEffectiveDate(qriPartition);
		}
		// save the personal opportunities
		for (List<Opportunity> partition : ListUtils.partition(opportunities, OpportunityJDBCRepo.SQL_BATCH_SIZE)) {
			saveOpportunityEffectiveDate(partition);
		}

		// update customer accounts and then save the commercial opportunities
		for (List<Opportunity> partition : ListUtils.partition(commercialOpportunities, OpportunityJDBCRepo.SQL_BATCH_SIZE)) {
			//Update customer accounts for commercial opportunities
			partition = opportunityHelper.updateCustomerAccounts(partition, null);
			saveOpportunityEffectiveDate(partition);
		}
	}

	private void saveOpportunityEffectiveDate(List<Opportunity> opportunities) {
		opportunityRepoHelper.saveAll(opportunities);
	}

	protected OppChangeFieldResult updateEffectiveDate(OppChangeFieldResult oppChangeFieldResult,
													   LocalDate updatedEffectiveDate) {

		String effDate = updatedEffectiveDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
		try {
			oppChangeFieldResult.setOpportunity(updateEffectiveDate(oppChangeFieldResult.getOpportunity(), effDate));
			oppChangeFieldResult.setFieldUpdatedValue(effDate);
		} catch (Exception e) {
			oppChangeFieldResult.isError(e.getMessage());
		}
		return oppChangeFieldResult;
	}

	private Opportunity updateEffectiveDate(Opportunity o, String updatedEffectiveDate) throws Exception {
		o.setEffectiveDate(updatedEffectiveDate);
		final String updatedXmlString = XmlHelper.getDocumentString(updateLeadEffectiveDate(o));
		o.setData(updatedXmlString);
		return o;
	}

	private Document updateLeadEffectiveDate(Opportunity opportunity) throws Exception {
		Document xmlDoc = XmlHelper.getDocument(opportunity.getData());
		int policyTerm = Opportunity.calcNewPolicyTerm(opportunity.getBusinessType(), xmlDoc);
		String newExpirationDate = LocalDate.parse(opportunity.getEffectiveDate()).plusMonths(policyTerm)
				.format(DateTimeFormatter.ISO_LOCAL_DATE);

		if (LineType.Business.equals(opportunity.getLineType())) {
			BusinessAcordHelper.setEffectiveDt(xmlDoc, opportunity.getEffectiveDate());
			BusinessAcordHelper.setExpirationDt(xmlDoc, newExpirationDate);
		} else {
			AcordHelper.setEffectiveDt(xmlDoc, opportunity.getEffectiveDate());
			AcordHelper.setExpirationDt(xmlDoc, newExpirationDate);
		}

		return xmlDoc;
	}

	protected boolean isCurrentDateSameAsEarliestDate(LocalDate earliestEffectiveDateForPackage,
													  LocalDate currentEffectiveDate) {
		return currentEffectiveDate != null && currentEffectiveDate.equals(earliestEffectiveDateForPackage);
	}

	protected LocalDate calculateNewEffectiveDate(Opportunity opp, LocalDate startDate) {
		LocalDate effectiveDate = LocalDate.parse(opp.getEffectiveDate());

		int policyTerm = Opportunity.calcNewPolicyTerm(opp);
		// if the effective date is before start date keep adding policy term until we
		// get past the start date.
		while (effectiveDate.isBefore(startDate)) {
			effectiveDate = effectiveDate.plusMonths(policyTerm);
		}

		return effectiveDate;
	}
}
