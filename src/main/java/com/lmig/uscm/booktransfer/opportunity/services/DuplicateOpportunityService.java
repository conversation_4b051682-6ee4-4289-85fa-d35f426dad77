package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service to handle duplicate opportunity detection and management.
 * Implements the stop-gap solution to prevent overwriting customer policy records
 * when there are active duplicate opportunities.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DuplicateOpportunityService {

    private final OpportunityJDBCRepo opportunityJDBCRepo;

    /**
     * Finds duplicate opportunities for the given opportunity based on customer matching criteria:
     * - Prior policy number (priorCarrierGuid)
     * - Line of Business (businessType)
     * - Book ID (bookTransferID)
     *
     * @param opportunity The opportunity to find duplicates for
     * @return List of duplicate opportunities (excluding the input opportunity)
     */
    public List<Opportunity> findDuplicateOpportunities(Opportunity opportunity) {
        log.debug("Finding duplicate opportunities for opportunityId: {}", opportunity.getOpportunityId());
        
        if (StringUtils.isEmpty(opportunity.getPriorCarrierGuid()) || 
            StringUtils.isEmpty(opportunity.getBusinessType())) {
            log.warn("Cannot find duplicates - missing required fields for opportunityId: {}", 
                    opportunity.getOpportunityId());
            return List.of();
        }

        List<Opportunity> duplicates = opportunityJDBCRepo.findOpportunitiesByCustomerCriteria(
                opportunity.getPriorCarrierGuid(),
                opportunity.getBusinessType(),
                opportunity.getBookTransferID()
        );

        // Exclude the current opportunity from the results
        List<Opportunity> filteredDuplicates = duplicates.stream()
                .filter(opp -> opp.getOpportunityId() != opportunity.getOpportunityId())
                .collect(Collectors.toList());

        log.debug("Found {} duplicate opportunities for opportunityId: {}",
                filteredDuplicates.size(), opportunity.getOpportunityId());
        
        return filteredDuplicates;
    }

    /**
     * Finds the latest active opportunity from a list of opportunities.
     * Active means not withdrawn, not issued, and not heritage.
     *
     * @param opportunities List of opportunities to search
     * @return Optional containing the latest active opportunity, or empty if none found
     */
    public Optional<Opportunity> findLatestActiveOpportunity(List<Opportunity> opportunities) {
        log.debug("Finding latest active opportunity from {} opportunities", opportunities.size());
        
        return opportunities.stream()
                .filter(this::isActiveOpportunity)
                .max(Comparator.comparingInt(Opportunity::getOpportunityId)); // Latest by ID (creation order)
    }

    /**
     * Checks if there are active duplicate opportunities for the given opportunity.
     * This is the main method used by the stop-gap solution.
     *
     * @param opportunity The opportunity being withdrawn
     * @return Optional containing the latest active duplicate opportunity, or empty if none found
     */
    public Optional<Opportunity> findLatestActiveDuplicateOpportunity(Opportunity opportunity) {
        log.debug("Checking for active duplicate opportunities for opportunityId: {}",
                opportunity.getOpportunityId());
        
        List<Opportunity> duplicates = findDuplicateOpportunities(opportunity);
        Optional<Opportunity> latestActive = findLatestActiveOpportunity(duplicates);
        
        if (latestActive.isPresent()) {
            log.debug("Found latest active duplicate opportunity: {} for withdrawn opportunity: {}",
                    latestActive.get().getOpportunityId(), opportunity.getOpportunityId());
        } else {
            log.debug("No active duplicate opportunities found for opportunityId: {}",
                    opportunity.getOpportunityId());
        }
        
        return latestActive;
    }

    /**
     * Determines if an opportunity is active (not withdrawn, issued, or heritage).
     *
     * @param opportunity The opportunity to check
     * @return true if the opportunity is active, false otherwise
     */
    private boolean isActiveOpportunity(Opportunity opportunity) {
        try {
            OpportunityStatus status = OpportunityStatus.getOppStatusFromCode(opportunity.getStatus());
            boolean isActive = status != OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN &&
                              status != OpportunityStatus.OPPORTUNITY_STATUS_ISSUED &&
                              status != OpportunityStatus.OPPORTUNITY_STATUS_HERITAGE;
            
            log.debug("Opportunity {} status: {} - Active: {}", 
                     opportunity.getOpportunityId(), status.getOppStatusValue(), isActive);
            
            return isActive;
        } catch (Exception e) {
            log.warn("Unable to determine status for opportunity: {} - treating as inactive", 
                    opportunity.getOpportunityId(), e);
            return false;
        }
    }

    /**
     * Checks if the given opportunity is being withdrawn.
     *
     * @param opportunity The opportunity to check
     * @return true if the opportunity is being withdrawn, false otherwise
     */
    public boolean isOpportunityBeingWithdrawn(Opportunity opportunity) {
        try {
            OpportunityStatus status = OpportunityStatus.getOppStatusFromCode(opportunity.getStatus());
            return status == OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN;
        } catch (Exception e) {
            log.warn("Unable to determine status for opportunity: {}", opportunity.getOpportunityId(), e);
            return false;
        }
    }

    /**
     * Determines if customer policy should be updated based on the stop-gap logic.
     * Returns true if:
     * 1. The opportunity is not being withdrawn, OR
     * 2. The opportunity is being withdrawn but there are no active duplicates
     *
     * @param opportunity The opportunity being processed
     * @return true if customer policy should be updated, false otherwise
     */
    public boolean shouldUpdateCustomerPolicy(Opportunity opportunity) {
        if (!isOpportunityBeingWithdrawn(opportunity)) {
            log.debug("Opportunity {} is not being withdrawn - should update customer policy", 
                     opportunity.getOpportunityId());
            return true;
        }

        // Opportunity is being withdrawn - check for active duplicates
        Optional<Opportunity> activeDuplicate = findLatestActiveDuplicateOpportunity(opportunity);
        boolean shouldUpdate = activeDuplicate.isEmpty();
        
        log.info("Opportunity {} is being withdrawn - Active duplicates found: {} - Should update: {}", 
                opportunity.getOpportunityId(), activeDuplicate.isPresent(), shouldUpdate);
        
        return shouldUpdate;
    }

    /**
     * Gets the opportunity that should be used to update the customer policy.
     * If the current opportunity is being withdrawn and there are active duplicates,
     * returns the latest active duplicate. Otherwise, returns the current opportunity.
     *
     * @param opportunity The opportunity being processed
     * @return The opportunity to use for customer policy update
     */
    public Opportunity getOpportunityForCustomerPolicyUpdate(Opportunity opportunity) {
        if (!isOpportunityBeingWithdrawn(opportunity)) {
            return opportunity;
        }

        Optional<Opportunity> activeDuplicate = findLatestActiveDuplicateOpportunity(opportunity);
        if (activeDuplicate.isPresent()) {
            log.info("Using active duplicate opportunity {} instead of withdrawn opportunity {} for customer policy update", 
                    activeDuplicate.get().getOpportunityId(), opportunity.getOpportunityId());
            return activeDuplicate.get();
        }

        return opportunity;
    }
}
