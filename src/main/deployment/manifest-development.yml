applications:
  - name: opportunityservice-development
    routes:
      - route: opportunityservice-development.pdc.np.paas.lmig.com
    buildpacks:
      - datadog_supply
      - java_current
    instances: 3
    memory: 1024M
    timeout: 180
    env:
      # sfStreamMongoDev - To create mongo client bean for dev salesforce streaming events db
      # transformationClient - To create oauth webclient bean for transformation engine service
      SPRING_PROFILES_ACTIVE: "dev,sfStreamMongoDev,transformationClient"
      JAVA_OPTS: "-Dlog4j2.formatMsgNoLookups=true"
      DD_SERVICE: ${env.ARTIFACT_KEY}
      DD_VERSION: ${inputs.build-number}
      DD_ENV: ${env.ENVIRONMENT_KEY}
      DD_RUNTIME_METRICS_ENABLED: true
      DD_PROFILING_ENABLED: true
      DD_TRACE_ENABLED: true
      DD_JMXFETCH_ENABLED: true
      DD_TAGS: team:quotabotz
      JBP_CONFIG_OPEN_JDK_JRE: '{ jre: { version: 17.+}}'
      AZURE_AQE_DB_UN: ${env.SECRET__BT_SHARED_AZURE_SQL_DB_DEVELOPMENT__READ_WRITE__USERNAME}
      AZURE_AQE_DB_PW: ${env.SECRET__BT_SHARED_AZURE_SQL_DB_DEVELOPMENT__READ_WRITE__PASSWORD}
      AZURE_AQE_DB_NAME: ${env.SECRET__BT_SHARED_AZURE_SQL_DB_DEVELOPMENT__READ_WRITE__DATABASE_NAME}
      AZURE_AQE_DB_PORT: ${env.SECRET__BT_SHARED_AZURE_SQL_DB_DEVELOPMENT__READ_WRITE__PORT}
      AZURE_AQE_DB_HOST: ${env.SECRET__BT_SHARED_AZURE_SQL_DB_DEVELOPMENT__READ_WRITE__HOST}
      QUOTE_ADAPTER_URL: ${env.SECRET__BLQUOTINGAPIS__QUOTEAPI}
      QUOTE_ADAPTER_AUDIENCE: ${env.QUOTING_BL_ADAPTER__AUDIENCE}
      OPP_CLIENT_ID: ${env.SECRET__OPPCLIENT_D__ID}
      OPP_CLIENT_SECRET: ${env.SECRET__OPPCLIENT_D__SECRET}
      TRANSFORMATION_AUDIENCE: ${env.TRANSFORMSVC_D__AUDIENCE}
      SALESFORCE_MONGO_UN: ${env.SECRET__BT_SALESFORCE_STREAMING_EVENTS_DB_DEVELOPMENT__READ_WRITE__USERNAME}
      SALESFORCE_MONGO_PW: ${env.SECRET__BT_SALESFORCE_STREAMING_EVENTS_DB_DEVELOPMENT__READ_WRITE__PASSWORD}
      AUDIENCE: ${env.OAUTH__API__ID}
      SENSITIVE_API_ACCESS_TOKEN: ${env.SENSITIVE_API_DEV__ACCESSTOKENURL}
      SENSITIVE_API_SCOPES: ${env.SENSITIVE_API_DEV__SCOPES}
      QUOTING_GUIDELINE_API_ACCESS_TOKEN: ${env.QUOTING_GUIDELINE_API_DEV__ACCESSTOKENURL}
      QUOTING_GUIDELINE_API_SCOPES: ${env.QUOTING_GUIDELINE_API_DEV__SCOPES}
      AUDIT_LOG_API_AUDIENCE: ${env.AUDIT_LOG_API_DEV__ACCESSTOKENURL}
      AUDIT_LOG_API_SCOPES: ${env.AUDIT_LOG_API_DEV__SCOPES}
      UPLOAD_PREPROCESSOR_ACCESS_TOKEN_URI: ${env.UPLOAD_PREPROCESSOR_API_DEV__ACCESSTOKENURL}
      UPLOAD_PREPROCESSOR_API_SCOPES: ${env.UPLOAD_PREPROCESSOR_API_DEV__SCOPES}
      BT_PAYMENT_SERVICE_API_ACCESS_TOKEN_URI: ${env.PAYMENT_SVC_DEV__ACCESSTOKENURL}
      BT_PAYMENT_SERVICE_API_SCOPES: ${env.PAYMENT_SVC_DEV__SCOPES}
      BT_CUSTOMER_ACCOUNT_SERVICE_API_ACCESS_TOKEN_URI: ${env.BOOK_TRANSFER_CUSTOMER_ACCOUNT_SERVICE__ACCESSTOKENURL}
      BT_CUSTOMER_ACCOUNT_SERVICE_API_SCOPES: ${env.BOOK_TRANSFER_CUSTOMER_ACCOUNT_SERVICE__SCOPES}
      ADDRESS_CLEANSE_CLIENT_ID: ${env.SECRET__OPPORTUNITY_SERVICE_AZURE_CLIENT_DEVELOPMENT__ID}
      ADDRESS_CLEANSE_CLIENT_SECRET: ${env.SECRET__OPPORTUNITY_SERVICE_AZURE_CLIENT_DEVELOPMENT__SECRET}
      EFT_PAYMENT_CLIENT_ID: ${env.SECRET__APIGEECREDS_DEV_CLOUD_FOUNDRY_NON_PROD_PDC_INTERNAL_DEVELOP__CLIENT_ID}
      EFT_PAYMENT_CLIENT_SECRET: ${env.SECRET__APIGEECREDS_DEV_CLOUD_FOUNDRY_NON_PROD_PDC_INTERNAL_DEVELOP__CLIENT_SECRET}
      BT_PROPERTY_INFO_SERVICE_API_ACCESS_TOKEN_URI: ${env.PROPERTYINFOSVC__ACCESSTOKENURL}
      BT_PROPERTY_INFO_SERVICE_API_SCOPES: ${env.PROPERTYINFOSVC__SCOPES}
      QUOTE_REPORT_SERVICE_API_ACCESS_TOKEN_URI: ${env.QUOTEREPORTSVC__ACCESSTOKENURL}
      QUOTE_REPORT_SERVICE_API_SCOPES: ${env.QUOTEREPORTSVC__SCOPES}
    services:
      - autoscaler
      - uploadqueue
    blue-green:
      health-check-endpoint: /health
    autoscaler:
      instance_limits:
        min: 1
        max: 3
      rules:
        - rule_type: cpu
          threshold:
            min: 25
            max: 75
        - rule_type: memory
          threshold:
            min: 25
            max: 75
      scheduled_limit_changes: [ ]