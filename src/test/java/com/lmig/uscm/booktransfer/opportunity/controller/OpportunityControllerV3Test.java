package com.lmig.uscm.booktransfer.opportunity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lmig.uscm.booktransfer.booktransferservice.client.BookTransferService;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferDTO;
import com.lmig.uscm.booktransfer.booktransferservice.client.domain.BookTransferException;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityFilterRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.OpportunityXmlData;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.MasterOppSPQEMeta;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationResponse;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.CustomFilterRequestForOppIds;
import com.lmig.uscm.booktransfer.opportunity.domain.customfilters.EffectiveDateRange;
import com.lmig.uscm.booktransfer.opportunity.repo.LobGateway;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.BTPaymentServiceHelper;
import com.lmig.uscm.booktransfer.opportunity.services.CustomerAccountHelper;
import com.lmig.uscm.booktransfer.opportunity.services.DownloadZipFileHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityCreationHelper;
import com.lmig.uscm.booktransfer.opportunity.services.OpportunityHelper;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportItemHelper;
import com.lmig.uscm.booktransfer.opportunity.services.QuotingGuidelineHelper;
import com.lmig.uscm.booktransfer.opportunity.services.SensitiveDataHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.FieldChangeHelper;
import com.lmig.uscm.booktransfer.opportunity.services.fieldchanger.effectivedate.UpdateEffectiveDateHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.PersonalAcordXPaths;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import com.lmig.usconsumermarkets.booktransfer.btquotingguidelinesservice.client.domain.QuotingGuidelineException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.nullable;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class OpportunityControllerV3Test {

	@Mock
	OpportunityRepoHelper opportunityRepoHelper;
	@Mock
	FieldChangeHelper fieldChangeHelper;
	@Mock
	BookTransferService bookTransferService;
	@Mock
	DownloadZipFileHelper downloadZipFileHelper;
	@Mock
	OpportunityCreationHelper opportunityCreationHelper;
	@Mock
	LobGateway lobGateway;
	@Mock
	QuoteReportItemHelper quoteReportItemHelper;
	@Mock
	CustomerAccountHelper customerAccountHelper;

	SensitiveDataHelper sensitiveDataHelper = mock(SensitiveDataHelper.class);

	OpportunityHelper opportunityHelper;

	@Mock
	OpportunityHelper opportunityHelper2;

	OpportunityControllerV3 opportunityControllerV3;

	EffectiveDateRange dateRange;

	private Map<String, UpdateEffectiveDateHelper> updateEffectiveDateDynamicMap;

	MockMvc mvc;
	ObjectMapper objectMapper = new ObjectMapper();

	private List<Integer> oppIdsRequest;

	@BeforeEach
	public void setup() {
		opportunityHelper = new OpportunityHelper(
			quoteReportItemHelper,
				bookTransferService,
				null,
				null,
				null,
				null,
				sensitiveDataHelper,
				opportunityRepoHelper,
				null,
				null,
				null,
				null);

		opportunityControllerV3 = new OpportunityControllerV3(
				opportunityHelper,
				opportunityRepoHelper,
				downloadZipFileHelper,
				fieldChangeHelper,
				customerAccountHelper,
				updateEffectiveDateDynamicMap,
				opportunityCreationHelper,
				lobGateway
		);
		mvc = MockMvcBuilders
				.standaloneSetup(opportunityControllerV3)
				.setControllerAdvice(new OpportunityAdvice(null))
				.build();
	}


	@Test
	void test_GET() throws Exception {
		doReturn(new Opportunity()).when(opportunityRepoHelper).findOpportunityById(anyInt());
		mvc.perform(get("/v3/opportunity/1"))
				.andExpect(status().isOk())
				.andExpect(content().string(objectMapper.writeValueAsString(new Opportunity())));
	}

	/**
	 * Tests happy path for getOpportunityXml with no detokenization
	 *
	 * GIVEN an Opp with Id 1 exists
	 * AND the Opp has XML
	 * WHEN GET /v3/opportunity/1/xml request is made
	 * AND no query param detokenization is passed
	 * THEN the xml for opp 1 is returned
	 * AND xml should not be detokenized
	 */
	@Test
	void test_GET_getOpportunityXml_no_detokenize_with_matching_oppXml() throws Exception {
		// GIVEN an Opp with Id 1 exists and has XML
		String oppXml = "<ACORD><node>test</node><InsuranceSvcRq><HomePolicyQuoteInqRq><Producer><ProducerInfo><ItemIdInfo><AgencyId/></ItemIdInfo><ContractNumber>876234</ContractNumber></ProducerInfo></Producer></HomePolicyQuoteInqRq></InsuranceSvcRq></ACORD>";
		String result =  "<ACORD><node>test</node><InsuranceSvcRq><HomePolicyQuoteInqRq><Producer><ProducerInfo><ItemIdInfo><AgencyId/><AgencyId originalContractNumber=\"876234\">876234</AgencyId></ItemIdInfo><ContractNumber/></ProducerInfo></Producer></HomePolicyQuoteInqRq></InsuranceSvcRq></ACORD>";
		BookTransferDTO bookTransferDTO = new BookTransferDTO();
		bookTransferDTO.setRampCode("rampCode");
		bookTransferDTO.setSubCode("subCode");
		doReturn(getMockOppDataFromDb(oppXml)).when(opportunityRepoHelper).getDataXmlForOppIds(anySet());
		doReturn(bookTransferDTO).when(bookTransferService).findByBookTransferId(anyInt());

		// WHEN GET /v3/opportunity/1/xml request is made
		// THEN the xml for opp 1 is returned
		mvc.perform(get("/v3/opportunity/1/xml"))
				.andExpect(status().isOk())
				.andExpect(content().string(result));

		// AND xml should not be detokenized
		verify(sensitiveDataHelper, times(0)).deTokenizeXml(any(), anyString());
		verify(bookTransferService, times(1)).findByBookTransferId(anyInt());
	}

	/**
	 * Tests happy path for getOpportunityXml with detokenization
	 *
	 * GIVEN an Opp with Id 1 exists
	 * AND the Opp has XML
	 * WHEN GET /v3/opportunity/1/xml request is made
	 * AND query param detokenization is passed as true
	 * THEN the xml for opp 1 is returned
	 * AND xml should be detokenized
	 */
	@Test
	void test_GET_getOpportunityXml_and_detokenize_with_matching_oppXml() throws Exception {
		// GIVEN an Opp with Id 1 exists and has XML
		String oppXml = "<ACORD><node>test</node><InsuranceSvcRq><HomePolicyQuoteInqRq><Producer><ProducerInfo><ItemIdInfo><AgencyId/></ItemIdInfo><ContractNumber/></ProducerInfo></Producer></HomePolicyQuoteInqRq></InsuranceSvcRq></ACORD>";
		String result = "<ACORD><node>test</node><InsuranceSvcRq><HomePolicyQuoteInqRq><Producer><ProducerInfo><ItemIdInfo><AgencyId/></ItemIdInfo><ContractNumber/></ProducerInfo></Producer></HomePolicyQuoteInqRq></InsuranceSvcRq></ACORD>";
		BookTransferDTO bookTransferDTO = new BookTransferDTO();
		bookTransferDTO.setRampCode("rampCode");
		bookTransferDTO.setSubCode("subCode");
		doReturn(getMockOppDataFromDb(oppXml)).when(opportunityRepoHelper).getDataXmlForOppIds(anySet());
		doReturn(XmlHelper.getDocument(oppXml)).when(sensitiveDataHelper).deTokenizeXml(any(Document.class), anyString());
		doReturn(bookTransferDTO).when(bookTransferService).findByBookTransferId(anyInt());

		// WHEN GET /v3/opportunity/1/xml request is made
		// THEN the xml for opp 1 is returned
		mvc.perform(get("/v3/opportunity/1/xml?detokenize=true"))
				.andExpect(status().isOk())
				.andExpect(content().string(result));

		// AND xml should not be detokenized
		verify(sensitiveDataHelper, times(1)).deTokenizeXml(any(), anyString());
		verify(bookTransferService, times(1)).findByBookTransferId(anyInt());
	}

	/**
	 * GIVEN an Opp with Id 1 exists
	 * AND the Opp has no XML
	 * WHEN GET /v3/opportunity/1/xml request is made
	 * THEN a 500 is thrown
	 */
	@Test
	void test_GET_getOpportunityXml_and_no_matching_oppXml() throws Exception {
		// GIVEN an Opp with Id 1 exists and has no XML
		doReturn(new ArrayList<OpportunityXmlData>()).when(opportunityRepoHelper).getDataXmlForOppIds(anySet());

		// WHEN GET /v3/opportunity/1/xml request is made
		// THEN the xml for opp 1 is returned
		mvc.perform(get("/v3/opportunity/1/xml?detokenize=true"))
				.andExpect(status().isInternalServerError());

		// AND xml should not be detokenized
		verify(sensitiveDataHelper, times(0)).deTokenizeXml(any(), anyString());
	}

	/**
	 * Given that I have list of opp ids When I make the request to get the effective date range Then it should get min
	 * and max effectivedates
	 */
	@Test
	void testGetEffectiveDateRange() throws Exception {
		opportunityControllerV3.setOpportunityRepoHelper(new OpportunityRepoHelper(null, null) {
			@Override
			public EffectiveDateRange getCustomFilterOpportunitiesEffectiveDateRange(List<Integer> oppIds) {
				oppIdsRequest = oppIds;
				return dateRange;
			}

		});
		dateRange = new EffectiveDateRange();
		dateRange.setMinEffectiveDate("2019-01-01");
		dateRange.setMaxEffectiveDate("2019-01-01");

		oppIdsRequest = null;
		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(1);
		mvc.perform(post("/v3/opportunity/effectiveDateRange").contentType(MediaType.APPLICATION_JSON)
						.content(objectMapper.writeValueAsString(oppIds))).andExpect(status().isOk())
				.andExpect(content().string(objectMapper.writeValueAsString(dateRange)));
		assertEquals(1, oppIdsRequest.size());
		assertEquals(1, oppIdsRequest.get(0));
	}

	@Test
	void testGetOppDetailsForQuoteResult() throws Exception {
		mvc.perform(post("/v3/opportunity/opportunity-details").contentType(MediaType.APPLICATION_JSON)
				.content(objectMapper.writeValueAsString(new OpportunityFilterRequest()))).andExpect(status().isOk());
	}

	@Test
	void testXmlValidationSuccess() throws Exception {
		Opportunity validateXML = new Opportunity();
		validateXML.setData("<ACORD></ACORD>");
		mvc.perform(post("/v3/opportunity/validateXML").contentType(MediaType.APPLICATION_JSON)
				.content(objectMapper.writeValueAsString(validateXML))).andExpect(status().isOk());
	}

	@Test
	void testXmlValidationError() throws Exception {
		Opportunity opportunity = new Opportunity();
		opportunity.setData("<ACORD><ForrestForcing></ACORD>");
		mvc.perform(post("/v3/opportunity/validateXML").contentType(MediaType.APPLICATION_JSON)
				.content(objectMapper.writeValueAsString(opportunity))).andExpect(status().isBadRequest());
	}

	@Test
	void testUpdateOppXMLFail() throws Exception {
		opportunityControllerV3.setOpportunityHelper(new OpportunityHelper(
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				opportunityRepoHelper,
				null,
				null,
				null, null) {
			@Override
			public void updateOpportunityXML(Opportunity newOpportunity) throws Exception {
				throw new Exception("Unable to update");
			}
		});
		mvc.perform(post("/v3/opportunity/updateOppXML").contentType(MediaType.APPLICATION_JSON)
				.content(objectMapper.writeValueAsString(new Opportunity()))).andExpect(status().isInternalServerError());

	}

	@Test
	void testGetOpportunityForEdit_masksData() throws Exception {

		Document autoDoc = XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml");
		String autoXml = XmlHelper.getDocumentString(autoDoc);

		Opportunity expectedOpportunity = new Opportunity(autoXml, autoXml);

		setUpObjectUnderTest(new Opportunity(autoXml, autoXml));

		Opportunity actualOpportunity =
				opportunityControllerV3.getOpportunityForEdit(expectedOpportunity.getOpportunityId());

		assertNotEquals(expectedOpportunity.getData(), actualOpportunity.getData());
		assertNotEquals(expectedOpportunity.getOriginalXML(), actualOpportunity.getOriginalXML());

		Document maskedDoc = XmlHelper.getDocument(actualOpportunity.getData());

		NodeList dlNums = XmlHelper.getNodeList(maskedDoc, PersonalAcordXPaths.DRIVERS_LICENSE);
		NodeList birthDts = XmlHelper.getNodeList(maskedDoc, PersonalAcordXPaths.DATE_OF_BIRTH);

		for (Node maskedNode : XmlHelper.nodeListToList(dlNums)) {
			assertNotNull(maskedNode.getAttributes().getNamedItem("isMasked"));
		}

		for (Node maskedNode : XmlHelper.nodeListToList(birthDts)) {
			assertNotNull(maskedNode.getAttributes().getNamedItem("isMasked"));
		}
	}

	@Test
	void testGetOpportunityForEdit_whenErrors_nullifiesData() throws Exception {

		Opportunity expectedOpportunity = new Opportunity("<some bad xml data />","<some bad xml data />");

		setUpObjectUnderTest(expectedOpportunity);

		Opportunity actualOpportunity =
				opportunityControllerV3.getOpportunityForEdit(expectedOpportunity.getOpportunityId());

		assertNull(actualOpportunity.getData());
		assertNull(actualOpportunity.getOriginalXML());
	}


	/**
	 * Given I have a list of oppoids Then get the booktransfer ids associated to the oppids @throws Exception @throws
	 */
	@Test
	void testOpportunityPageOppIds() throws Exception {

		OpportunityRepoHelper mockRepoHelper = mock(OpportunityRepoHelper.class);
		doReturn(List.of(1)).when(mockRepoHelper).getCustomFilterOppIds(any());
		opportunityControllerV3.setOpportunityRepoHelper(mockRepoHelper);

		ArgumentCaptor<CustomFilterRequestForOppIds> captor = ArgumentCaptor.forClass(CustomFilterRequestForOppIds.class);
		CustomFilterRequestForOppIds requestFilter = new CustomFilterRequestForOppIds();
		requestFilter.setStatuses(new ArrayList<>());

		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(1);

		String requestBody = objectMapper.writeValueAsString(requestFilter);
		String expectedOpps = objectMapper.writeValueAsString(oppIds);

		mvc.perform(post("/v3/opportunity/opportunityPageOppIds").contentType(MediaType.APPLICATION_JSON)
				.content(requestBody)).andExpect(status().isOk()).andExpect(content().string(expectedOpps));

		verify(opportunityControllerV3.getOpportunityRepoHelper()).getCustomFilterOppIds(captor.capture());

		final CustomFilterRequestForOppIds resultingRequest = captor.getValue();

		assertEquals(LineType.All, resultingRequest.getLineType());
	}

	@Test
	void testCreateOpportunity() throws Exception {
		OpportunityCreationRequest request = new OpportunityCreationRequest();
		// set properties of request as needed for your test

		OpportunityCreationResponse expectedResponse = new OpportunityCreationResponse();
		// set properties of expectedResponse as needed for your test

		when(opportunityCreationHelper.createOpportunityFromOpportunityRequest(anyBoolean(), any())).thenReturn(expectedResponse);

		mvc.perform(post("/v3/opportunity/create")
				.contentType(MediaType.APPLICATION_JSON)
				.content(objectMapper.writeValueAsString(request)))
			.andExpect(status().isOk())
			.andExpect(content().string(objectMapper.writeValueAsString(expectedResponse)));
	}

	@Test
	void testOppCreation() throws Exception {
		when(opportunityCreationHelper.createOpportunityFromOpportunityRequest(anyBoolean(), any())).thenReturn(new OpportunityCreationResponse());
		mvc.perform(post("/v3/opportunity/create").contentType(MediaType.APPLICATION_JSON)
				.content(objectMapper.writeValueAsString(new OpportunityCreationRequest()))).andExpect(status().isOk())
			.andExpect(content().string(objectMapper.writeValueAsString(new OpportunityCreationResponse())));

	}

	@Test
	void testSPQEOppCreation() throws Exception {
		OpportunityCreationRequest request = new OpportunityCreationRequest();
		List<Integer> spqeOpportunities = new ArrayList<>();
		MasterOppSPQEMeta masterOppSPQEMeta = new MasterOppSPQEMeta(spqeOpportunities);
		request.setMasterOppSPQEMeta(masterOppSPQEMeta);
		when(opportunityCreationHelper.createOpportunityFromOpportunityRequest(anyBoolean(), any())).thenReturn(new OpportunityCreationResponse());
		mvc.perform(post("/v3/opportunity/SPQE").contentType(MediaType.APPLICATION_JSON)
				.content(objectMapper.writeValueAsString(request))).andExpect(status().isOk())
			.andExpect(content().string(objectMapper.writeValueAsString(new OpportunityCreationResponse())));

	}

	@Test
	void testReUploadOpportunity() throws Exception {
		List<Integer> oppIds = new ArrayList<>();
		oppIds.add(1);
		mvc.perform(post("/v3/opportunity/reupload").contentType(MediaType.APPLICATION_JSON)
				.content(objectMapper.writeValueAsString(oppIds))).andExpect(status().isOk())
			.andExpect(content().string("Opportunities Re-Uploaded"));

	}

	@Test
	void testOppStatusChangeForPersonalLineType() throws Exception {
		mvc.perform(post("/v3/opportunity/changeOpportunitiesStatus/personal?status=1").contentType(MediaType.APPLICATION_JSON)
				.content(objectMapper.writeValueAsString(List.of(1,2)))).andExpect(status().isOk());
		verify(opportunityRepoHelper).updateOpportunitiesStatus(any(), anyInt());
		verify(quoteReportItemHelper).updateQuoteReportItemForStatus(any(), anyString());
	}

	@Test
	void testOppStatusChangeForBusinessLineType() throws Exception {
		mvc.perform(post("/v3/opportunity/changeOpportunitiesStatus/business?status=1").contentType(MediaType.APPLICATION_JSON)
			.content(objectMapper.writeValueAsString(List.of(1,2)))).andExpect(status().isOk());
		verify(opportunityRepoHelper).updateOpportunitiesStatus(any(), anyInt());
		verify(quoteReportItemHelper, times(0)).updateQuoteReportItemForStatus(any(), anyString());
	}

	private void setUpObjectUnderTest(Opportunity expectedOpportunity) throws BookTransferException, QuotingGuidelineException {
		OpportunityRepoHelper opportunityRepoHelper1 = mock(OpportunityRepoHelper.class);
		BookTransferService bookTransferService = mock(BookTransferService.class);
		QuotingGuidelineHelper quotingGuidelineHelper = mock(QuotingGuidelineHelper.class);
		BTPaymentServiceHelper paymentServiceHelper = mock(BTPaymentServiceHelper.class);
		BookTransferDTO bookTransferDTO = new BookTransferDTO();
		bookTransferDTO.setFirstEffectiveDate(new Date());
		bookTransferDTO.setSalesforceCode("BT-12345");
		bookTransferDTO.setBookTransferID(12345);
		doReturn(expectedOpportunity).when(opportunityRepoHelper1).findOpportunityForEdit(anyInt());
		doReturn(bookTransferDTO)
				.when(bookTransferService).findByBookTransferId(anyInt());
		lenient().doReturn(null)
				.when(paymentServiceHelper).getPaymentInfoUsingBillingAccountNumber(nullable(String.class));

		OpportunityHelper opportunityHelper1 = new OpportunityHelper(
				null,
				bookTransferService,
				null,
				null,
				null,
				null,
				null,
				opportunityRepoHelper1,
				quotingGuidelineHelper,
				paymentServiceHelper,
				null, new ObjectMapper());

		opportunityControllerV3.setOpportunityHelper(opportunityHelper1);
	}

	private List<OpportunityXmlData> getMockOppDataFromDb(String xmlString){
		OpportunityXmlData oppData = OpportunityXmlData.builder()
			.opportunityId(1)
			.xmlData(xmlString)
			.build();
		return new ArrayList<>(List.of(oppData));
	}
}
