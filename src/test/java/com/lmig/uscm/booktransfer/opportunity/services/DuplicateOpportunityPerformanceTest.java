package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Performance tests for the DuplicateOpportunityService to ensure it can handle
 * bulk operations efficiently when withdrawing thousands of opportunities.
 */
@ExtendWith(MockitoExtension.class)
class DuplicateOpportunityPerformanceTest {

    @Mock
    private OpportunityJDBCRepo opportunityJDBCRepo;

    private DuplicateOpportunityService duplicateOpportunityService;

    @BeforeEach
    void setUp() {
        duplicateOpportunityService = new DuplicateOpportunityService(opportunityJDBCRepo);
    }

    @Test
    void testBulkWithdrawal_1000Opportunities_CompletesWithinTimeLimit() {
        // Given: 1000 opportunities to be withdrawn
        List<Opportunity> opportunities = createBulkOpportunities(1000);
        
        // Mock repository to return some duplicates for each opportunity
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria(anyString(), anyString(), anyInt()))
                .thenReturn(createMockDuplicates());

        // When: Process all opportunities and measure time
        long startTime = System.nanoTime();
        
        for (Opportunity opp : opportunities) {
            duplicateOpportunityService.getOpportunityForCustomerPolicyUpdate(opp);
        }
        
        long endTime = System.nanoTime();
        long durationMs = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);

        // Then: Should complete within reasonable time (e.g., 10 seconds for 1000 operations)
        assertTrue(durationMs < 10000, 
                String.format("Bulk processing took %d ms, expected < 10000 ms", durationMs));
        
        System.out.println(String.format("Processed 1000 opportunities in %d ms (avg: %.2f ms per opportunity)", 
                durationMs, (double) durationMs / 1000));
    }

    @Test
    void testBulkWithdrawal_5000Opportunities_CompletesWithinTimeLimit() {
        // Given: 5000 opportunities to be withdrawn
        List<Opportunity> opportunities = createBulkOpportunities(5000);
        
        // Mock repository to return some duplicates for each opportunity
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria(anyString(), anyString(), anyInt()))
                .thenReturn(createMockDuplicates());

        // When: Process all opportunities and measure time
        long startTime = System.nanoTime();
        
        for (Opportunity opp : opportunities) {
            duplicateOpportunityService.getOpportunityForCustomerPolicyUpdate(opp);
        }
        
        long endTime = System.nanoTime();
        long durationMs = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);

        // Then: Should complete within reasonable time (e.g., 30 seconds for 5000 operations)
        assertTrue(durationMs < 30000, 
                String.format("Bulk processing took %d ms, expected < 30000 ms", durationMs));
        
        System.out.println(String.format("Processed 5000 opportunities in %d ms (avg: %.2f ms per opportunity)", 
                durationMs, (double) durationMs / 5000));
    }

    @Test
    void testMemoryUsage_BulkProcessing_DoesNotCauseMemoryLeak() {
        // Given: Large number of opportunities
        List<Opportunity> opportunities = createBulkOpportunities(2000);
        
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria(anyString(), anyString(), anyInt()))
                .thenReturn(createMockDuplicates());

        // When: Process opportunities and measure memory
        Runtime runtime = Runtime.getRuntime();
        runtime.gc(); // Force garbage collection before measurement
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
        
        for (Opportunity opp : opportunities) {
            duplicateOpportunityService.findLatestActiveDuplicateOpportunity(opp);
        }
        
        runtime.gc(); // Force garbage collection after processing
        long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
        long memoryUsed = memoryAfter - memoryBefore;

        // Then: Memory usage should be reasonable (less than 100MB for 2000 operations)
        assertTrue(memoryUsed < 100 * 1024 * 1024, 
                String.format("Memory usage was %d bytes, expected < 100MB", memoryUsed));
        
        System.out.println(String.format("Memory used for 2000 operations: %.2f MB", 
                (double) memoryUsed / (1024 * 1024)));
    }

    @Test
    void testConcurrentAccess_MultipleThreads_HandlesCorrectly() throws InterruptedException {
        // Given: Multiple threads processing opportunities concurrently
        List<Opportunity> opportunities = createBulkOpportunities(500);
        
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria(anyString(), anyString(), anyInt()))
                .thenReturn(createMockDuplicates());

        int numThreads = 5;
        List<Thread> threads = new ArrayList<>();
        List<Exception> exceptions = new ArrayList<>();

        // When: Process opportunities concurrently
        for (int i = 0; i < numThreads; i++) {
            final int threadIndex = i;
            Thread thread = new Thread(() -> {
                try {
                    int startIndex = threadIndex * 100;
                    int endIndex = Math.min(startIndex + 100, opportunities.size());
                    
                    for (int j = startIndex; j < endIndex; j++) {
                        duplicateOpportunityService.getOpportunityForCustomerPolicyUpdate(opportunities.get(j));
                    }
                } catch (Exception e) {
                    synchronized (exceptions) {
                        exceptions.add(e);
                    }
                }
            });
            threads.add(thread);
            thread.start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join(10000); // 10 second timeout
        }

        // Then: No exceptions should occur
        assertTrue(exceptions.isEmpty(), 
                String.format("Concurrent processing failed with %d exceptions: %s", 
                        exceptions.size(), exceptions.toString()));
    }

    @Test
    void testDatabaseQueryOptimization_BulkOperations_MinimizesQueries() {
        // Given: Opportunities with same customer criteria (should reuse results)
        List<Opportunity> opportunities = createOpportunitiesWithSameCriteria(100);
        
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria("POLICY123", "HOME", 100))
                .thenReturn(createMockDuplicates());

        // When: Process all opportunities
        long startTime = System.nanoTime();
        
        for (Opportunity opp : opportunities) {
            duplicateOpportunityService.findLatestActiveDuplicateOpportunity(opp);
        }
        
        long endTime = System.nanoTime();
        long durationMs = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);

        // Then: Should complete quickly since all opportunities have same criteria
        assertTrue(durationMs < 1000, 
                String.format("Processing 100 opportunities with same criteria took %d ms, expected < 1000 ms", durationMs));
        
        System.out.println(String.format("Processed 100 opportunities with same criteria in %d ms", durationMs));
    }

    private List<Opportunity> createBulkOpportunities(int count) {
        List<Opportunity> opportunities = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            Opportunity opp = new Opportunity();
            opp.setOpportunityId(i);
            opp.setPriorCarrierGuid("POLICY" + (i % 100)); // Create some variety
            opp.setBusinessType(i % 2 == 0 ? "HOME" : "AUTO"); // Alternate between HOME and AUTO
            opp.setBookTransferID(100 + (i % 10)); // Create some variety in book IDs
            opp.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode());
            opp.setLineType(LineType.Business);
            opp.setData("<xml><test>data" + i + "</test></xml>");
            opp.setOriginalXML("<xml><original>data" + i + "</original></xml>"); // Set required originalXML field
            opportunities.add(opp);
        }
        
        return opportunities;
    }

    private List<Opportunity> createOpportunitiesWithSameCriteria(int count) {
        List<Opportunity> opportunities = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            Opportunity opp = new Opportunity();
            opp.setOpportunityId(i);
            opp.setPriorCarrierGuid("POLICY123"); // Same criteria
            opp.setBusinessType("HOME"); // Same criteria
            opp.setBookTransferID(100); // Same criteria
            opp.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode());
            opp.setLineType(LineType.Business);
            opp.setData("<xml><test>data" + i + "</test></xml>");
            opp.setOriginalXML("<xml><original>data" + i + "</original></xml>"); // Set required originalXML field
            opportunities.add(opp);
        }
        
        return opportunities;
    }

    private List<Opportunity> createMockDuplicates() {
        List<Opportunity> duplicates = new ArrayList<>();
        
        // Create a few mock duplicates with different statuses
        Opportunity active1 = new Opportunity();
        active1.setOpportunityId(9001);
        active1.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED.getOppStatusCode());
        active1.setData("<xml><test>active1</test></xml>");
        active1.setOriginalXML("<xml><original>active1</original></xml>"); // Set required originalXML field
        duplicates.add(active1);

        Opportunity active2 = new Opportunity();
        active2.setOpportunityId(9002);
        active2.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS.getOppStatusCode());
        active2.setData("<xml><test>active2</test></xml>");
        active2.setOriginalXML("<xml><original>active2</original></xml>"); // Set required originalXML field
        duplicates.add(active2);

        Opportunity withdrawn = new Opportunity();
        withdrawn.setOpportunityId(9003);
        withdrawn.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode());
        withdrawn.setData("<xml><test>withdrawn</test></xml>");
        withdrawn.setOriginalXML("<xml><original>withdrawn</original></xml>"); // Set required originalXML field
        duplicates.add(withdrawn);
        
        return duplicates;
    }
}
