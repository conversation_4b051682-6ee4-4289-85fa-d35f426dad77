/*
 * Copyright (C) 2018, Liberty Mutual Group
 *
 * Created on 12/4/2018 by n0312022
 */
package com.lmig.uscm.booktransfer.opportunity.services.builders;

import com.lmig.uscm.booktransfer.opportunity.client.domain.EFTPaymentAccountsResponse;
import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.QuoteType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OpportunityCreationRequest;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.OriginSource;
import com.lmig.uscm.booktransfer.opportunity.client.domain.creation.SourceType;
import com.lmig.uscm.booktransfer.opportunity.domain.Constants;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerAccountServiceResponseData;
import com.lmig.uscm.booktransfer.opportunity.domain.customeraccount.CustomerPolicy;
import com.lmig.uscm.booktransfer.opportunity.repo.helpers.OpportunityRepoHelper;
import com.lmig.uscm.booktransfer.opportunity.services.AddressCleanseService;
import com.lmig.uscm.booktransfer.opportunity.services.CustomerAccountHelper;
import com.lmig.uscm.booktransfer.opportunity.services.DuplicateOpportunityService;
import com.lmig.uscm.booktransfer.opportunity.services.EFTPaymentAccountsService;
import com.lmig.uscm.booktransfer.opportunity.services.QuoteReportItemHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.AcordHelper;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.BDDMockito;
import org.mockito.InOrder;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.reactive.function.client.WebClient;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import javax.xml.parsers.ParserConfigurationException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OpportunityBuilderTest {
	@Mock
	OpportunityRepoHelper opportunityRepoHelper;
	@Mock
	OpportunityConstructor opportunityConstructor;
	@Mock
	QuoteReportItemHelper quoteReportItemHelper;

	@Mock
	AddressCleanseService addressCleanseService;
	@Mock
	EFTPaymentAccountsService eftPaymentAccountsService;
	@Mock
	WebClient customerAccountWebClientMock;

	CustomerAccountHelper customerAccountHelper;

	String runRulesId;
	Document runRulesXml;
	private final String IMPORT_PACKAGE = "612d548bc5eef2601e472274";

	@BeforeEach
	public void setUpMock() throws Exception {
		lenient().doReturn(mockOpportunity()).when(opportunityConstructor)
				.buildInitialOpportunity(any(OpportunityCreationRequest.class));
		lenient().doAnswer(invocation -> invocation.getArguments()[1]).when(opportunityConstructor)
				.updateOpportunity(any(OpportunityCreationRequest.class), any(Opportunity.class), any(Document.class));
		lenient().doAnswer(invocation -> invocation.getArguments()[1]).when(opportunityConstructor)
				.checkForUpdatedSubCode(any(OpportunityCreationRequest.class), any(Opportunity.class), any(Document.class));
		lenient().doAnswer(invocation -> invocation.getArgument(0))
				.when(addressCleanseService).addCountiesToLocations(any(Document.class));

		lenient().doAnswer(invocation -> {
			Opportunity opportunity = (Opportunity) invocation.getArguments()[0];
			if (opportunity == null) {
				opportunity = new Opportunity();
			}
			opportunity.setOpportunityId(5);
			return opportunity;
		}).when(opportunityRepoHelper).save(any(Opportunity.class));

		lenient().doAnswer(invocation -> {
			Opportunity opportunity = (Opportunity) invocation.getArguments()[0];
			if (opportunity == null) {
				opportunity = new Opportunity();
			}
			opportunity.setOpportunityId(5);
			return opportunity;
		}).when(opportunityRepoHelper).updateOpportunity(any(Opportunity.class));

		lenient().doAnswer(invocation -> {
			runRulesId = invocation.getArgument(1);
			runRulesXml = invocation.getArgument(0);
			return runRulesXml;
		}).when(opportunityConstructor).runRules(any(Document.class), any(String.class), any());

		customerAccountHelper = Mockito.spy(new CustomerAccountHelper(customerAccountWebClientMock, "", null, Mockito.mock(DuplicateOpportunityService.class)));
		lenient().doReturn(CustomerAccountServiceResponseData.builder()
				.id("123")
				.customerPolicies(List.of(CustomerPolicy.builder()._id("1").build()))
				.build()
		).when(customerAccountHelper).createCustomerAccount(any(), any(), any());
	}

	/**
	 * Tests if an opportunity is created in the correct order
	 */
	@Test
	void testValidateCreationOfOppBuilder() throws Exception {
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		OpportunityBuilder opportunityBuilder = getUploadOpportunityHelperInstance(false);
		opportunityBuilder.uploadOpportunity(opportunityCreationRequest);
		verify(1);
	}

	private OpportunityCreationRequest buildOpportunityCreationRequestWithAutoXML()
			throws IOException, ParserConfigurationException, SAXException {
		Document doc = getDocument();

		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		opportunityCreationRequest.setUploadedACORD(doc);
		opportunityCreationRequest.setLineType(LineType.Personal);
		opportunityCreationRequest.setNbdRelationship("test");
		return opportunityCreationRequest;
	}

	private OpportunityBuilder getUploadOpportunityHelperInstance(boolean isForSPQE) {
		UploadOpportunityBuilder uploadOpportunityBuilder = new UploadOpportunityBuilder(opportunityRepoHelper,
				opportunityConstructor, quoteReportItemHelper, addressCleanseService, eftPaymentAccountsService,
				customerAccountHelper, isForSPQE, IMPORT_PACKAGE);
		// ReflectionTestUtils.setField(uploadOpportunityBuilder, "IMPORT_PACKAGE", IMPORT_PACKAGE);
		return Mockito.spy(uploadOpportunityBuilder);
	}

	private OpportunityBuilder getReUploadOpportunityHelperInstance(boolean isForSPQE) {
		ReuploadOpportunityBuilder reuploadOpportunityBuilder = new ReuploadOpportunityBuilder(opportunityRepoHelper,
				opportunityConstructor, quoteReportItemHelper, addressCleanseService, eftPaymentAccountsService,
				customerAccountHelper, isForSPQE, IMPORT_PACKAGE);
		// ReflectionTestUtils.setField(reuploadOpportunityBuilder, "IMPORT_PACKAGE", IMPORT_PACKAGE);
		return Mockito.spy(reuploadOpportunityBuilder);
	}

	private OpportunityBuilder getBLOpportunityBuilder(boolean isForSPQE) {
		BLUploadOpportunityBuilder blUploadOpportunityBuilder = new BLUploadOpportunityBuilder(opportunityRepoHelper,
				opportunityConstructor, quoteReportItemHelper, addressCleanseService, eftPaymentAccountsService,
				customerAccountHelper, isForSPQE, IMPORT_PACKAGE);
		return Mockito.spy(blUploadOpportunityBuilder);
	}

	/**
	 * Tests that for master opps we save the opportunity 2x with the Master Opp ID
	 */
	@Test
	@Disabled
	void testValidateCreationOfMasterOppBuilder() throws Exception {
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setAsMasterOpp();
		OpportunityBuilder opportunityBuilder = getUploadOpportunityHelperInstance(false);
		opportunityBuilder.uploadOpportunity(opportunityCreationRequest);
		verify(2);

	}

	/**
	 * Tests if an opportunity is created in the correct order for flat files
	 */
	@Test
	void testValidateCreationOfSPQEOppBuilder() throws Exception {
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		OpportunityBuilder opportunityBuilder = getUploadOpportunityHelperInstance(true);
		opportunityBuilder.uploadOpportunity(opportunityCreationRequest);
		verify(1);

	}

	@Test
	void testNullMasterOppIdStaysNull() throws Exception {
		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		opportunityCreationRequest.setMasterOpp(false);
		opportunityCreationRequest.setUploadedACORD(getDocument());
		opportunityCreationRequest.setLineType(LineType.Personal);
		OpportunityBuilder opportunityBuilder = getUploadOpportunityHelperInstance(true);
		opportunityBuilder.uploadOpportunity(opportunityCreationRequest);

		// Check that the Opportunity gets saved once (no re-save for updating masterOppId) and has masterOppId null
		Mockito.verify(opportunityRepoHelper, times(1))
				.save(argThat((Opportunity arg) -> arg.getMasterOppID() == null));
	}

	@Test
	void testNullMasterOppIdIsAddedIfNotNull() throws Exception {
		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		opportunityCreationRequest.setUploadedACORD(getDocument());
		opportunityCreationRequest.setMasterOpp(true);
		opportunityCreationRequest.setLineType(LineType.Personal);
		Opportunity opportunity = new Opportunity();
		opportunity.setOpportunityId(12376);
		BDDMockito.willReturn(opportunity).given(opportunityRepoHelper).save(any());
		OpportunityBuilder opportunityBuilder = getUploadOpportunityHelperInstance(true);
		opportunityBuilder.uploadOpportunity(opportunityCreationRequest);

		// Check that the Opportunity gets saved once and has masterOppId null
		Mockito.verify(opportunityRepoHelper, times(1))
				.save(argThat((Opportunity arg) -> StringUtils.isBlank(arg.getMasterOppID())));
		// Check that the Opportunity gets re-saved once with the expected masterOppId
		Mockito.verify(opportunityRepoHelper, times(1))
				.updateOpportunity(argThat((Opportunity arg) -> StringUtils.equals(arg.getMasterOppID(), "12376")));
	}

	private void verify(int timesRepoIsCalled) throws Exception {
		InOrder order;

		order = Mockito.inOrder(opportunityConstructor, quoteReportItemHelper, opportunityRepoHelper);
		order.verify(opportunityConstructor).buildInitialOpportunity(any(OpportunityCreationRequest.class));
		verify(order);

		order.verify(opportunityRepoHelper, times(timesRepoIsCalled)).save(any(Opportunity.class));
		order.verify(quoteReportItemHelper).buildAndSaveQuoteReportItem(any(Opportunity.class), isNull(), isNull());
	}

	private void verify(InOrder order) throws Exception {
		Mockito.verify(opportunityConstructor, times(0)).formatXmlForOpportunity(any(Document.class),
				any(OpportunityCreationRequest.class));
		order.verify(opportunityConstructor).runRules(any(Document.class), any(String.class), any());
		order.verify(opportunityConstructor).updateOpportunity(any(OpportunityCreationRequest.class), any(Opportunity.class), any(Document.class));
	}

	private Document getDocument() throws IOException, ParserConfigurationException, SAXException {
		String acordXml = getFileContents();
		return XmlHelper.getDocument(acordXml);
	}

	private String getFileContents() throws IOException {
		Path path = Paths.get("src/test/resources/xml/auto.xml");
		byte[] content = Files.readAllBytes(path);
		return new String(content);
	}

	private String getFileContentsWithNbd() throws IOException {
		Path path = Paths.get("src/test/resources/xml/autonbd.xml");
		byte[] content = Files.readAllBytes(path);
		return new String(content);
	}

	private Document getFileContents(String filePath) throws IOException, ParserConfigurationException, SAXException  {
		Path path = Paths.get(filePath);
		byte[] content = Files.readAllBytes(path);
		String acordXML = new String(content);
		return XmlHelper.getDocument(acordXML);
	}

	/**
	 * Given that we have successfully created an opp. And that quoteReport will fail Then i will log the error But
	 * Still return the opportunity
	 */
	@Test
	void testCreatingQuoteReportItemFailingShouldNotStopOppFromCreating() throws Exception {

		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);

		builder.uploadOpportunity(opportunityCreationRequest);

		// Check that the Opportunity gets saved once with the expected oppId
		Mockito.verify(opportunityRepoHelper, times(1))
				.save(argThat((Opportunity arg) -> arg.getOpportunityId() == 5));
	}

	/**
	 * Given I have a opportunity When i try to reupload then it should be updated with original xml after all rules
	 * applied
	 */
	@Test
	void testReUploadOpportunity() throws Exception {
		boolean isForSpqe = false;
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		OpportunityBuilder builder = getReUploadOpportunityHelperInstance(isForSpqe);
		Opportunity opportunity = new Opportunity();
		opportunity.setOpportunityId(oppId);
		opportunity.setOriginalXML(getFileContents());
		opportunity.setData(getFileContents());
		opportunity.setStatus(2);
		opportunity.setLineType(LineType.Personal);
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opportunity);
		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);
		assertEquals("7211-2503668", newOpportunity.getBillingAccountNumber());
		assertFalse(builder.isForSPQE);
		verifyForReUpload(isForSpqe);
	}

	@Test
	void testReUploadOpportunityWithIssuedStatus() throws Exception {
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		OpportunityBuilder builder = getReUploadOpportunityHelperInstance(false);
		Opportunity opportunity = new Opportunity();
		opportunity.setOpportunityId(oppId);
		opportunity.setOriginalXML(getFileContents());
		opportunity.setStatus(10);
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opportunity);

		assertThrows(Exception.class, () -> builder.uploadOpportunity(opportunityCreationRequest));
	}

	@Test
	void testUploadOpportunity_whenPlLineAndNullOriginSource_shouldSetOpportunityQuoteTypeToAutomatedValue() throws Exception {
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setLineType(LineType.Personal);
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);
		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);
		assertEquals(QuoteType.AUTOMATED, newOpportunity.getQuoteType());
	}

	@Test
	void testUploadOpportunity_whenBlLineAndNullOriginSource_shouldSetOpportunityQuoteTypeToAutomatedValue() throws Exception {
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setLineType(LineType.Business);
		opportunityCreationRequest.setSFDCID(123);
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);
		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);
		assertEquals(QuoteType.AUTOMATED, newOpportunity.getQuoteType());
	}

	@Test
	void testUploadOpportunity_nullBtMetaCreationOriginAndType_defaultUnknown() throws Exception {
		doAnswer(invocation -> invocation.getArgument(0))
				.when(addressCleanseService).addCountiesToLocations(any(Document.class));
		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		opportunityCreationRequest.setUploadedACORD(XmlHelper.getXmlDocumentFromPath("src/test/resources/xml/auto.xml"));
		opportunityCreationRequest.setLineType(LineType.Personal);
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);
		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);
		Document defaultDoc = XmlHelper.getDocument(newOpportunity.getOriginalXML());
		assertEquals(SourceType.UNKNOWN.getValue(), AcordHelper.getBTMetaCreationType(defaultDoc));
		assertEquals(OriginSource.UNKNOWN.getValue(), AcordHelper.getBTMetaCreationOrigin(defaultDoc));
	}

	@Test
	void testUploadOpportunity_whenNullLineTypeAndNullOriginSource_shouldSetOpportunityQuoteTypeToAutomatedValue() throws Exception {
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setLineType(LineType.Personal);
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);
		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);
		assertEquals(QuoteType.AUTOMATED, newOpportunity.getQuoteType());
	}

	@Test
	void testUploadOpportunity_whenPlLineType_shouldNotSetOrigXmlQuoteTypeToManualValue() throws Exception {
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setOriginSource(OriginSource.AQE);
		opportunityCreationRequest.setLineType(LineType.Personal);
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);

		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		Document originalXmlDoc = XmlHelper.getDocument(newOpportunity.getOriginalXML());
		assertEquals("", XmlHelper.nullSafeExtractFromXml(originalXmlDoc, "//QuoteType"));
	}

	@Test
	void testUploadOpportunity_withNBDRelationship_BL() throws Exception {
		boolean isForSpqe = false;
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		OpportunityBuilder builder = getBLOpportunityBuilder(isForSpqe);
		Opportunity opportunity = new Opportunity();
		//opportunity.setOpportunityId(oppId);
		opportunity.setOriginalXML(getFileContents());
		opportunity.setData(getFileContentsWithNbd());
		opportunity.setStatus(2);
		opportunity.setLineType(LineType.Personal);
		when(opportunityConstructor.buildInitialOpportunity(opportunityCreationRequest)).thenReturn(opportunity);
		//when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opportunity);
		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);
		assertEquals("testnbd", XmlHelper.getNodeFromDoc("//NBDRelationship", XmlHelper.getDocument(newOpportunity.getData())).getTextContent().trim());
	}

	@Test
	void testUploadOpportunity_withNBDRelationship() throws Exception {
		boolean isForSpqe = false;
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		OpportunityBuilder builder = getReUploadOpportunityHelperInstance(isForSpqe);
		Opportunity opportunity = new Opportunity();
		opportunity.setOpportunityId(oppId);
		opportunity.setOriginalXML(getFileContents());
		opportunity.setData(getFileContentsWithNbd());
		opportunity.setStatus(2);
		opportunity.setLineType(LineType.Personal);
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opportunity);
		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);
		assertEquals("testnbd", XmlHelper.getNodeFromDoc("//NBDRelationship", XmlHelper.getDocument(newOpportunity.getData())).getTextContent().trim());
	}

	@Test
	void testUploadOpportunity_shouldRunImportRulesOnce_whenUploadOpportunityAndNotSPQE() throws Exception {
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = new OpportunityCreationRequest();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setLineType(LineType.Personal);
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);
		builder.uploadOpportunity(opportunityCreationRequest);
		Mockito.verify(opportunityConstructor, times(1)).runRules(runRulesXml, IMPORT_PACKAGE, 0);
	}

	@Test
	void testUploadOpportunity_shouldSetOrigXmlAgentNumberWithOCRSubCode_whenOCRSubCodeExists() throws Exception {
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setSubCode("111111");
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);
		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		Document origXml = XmlHelper.getDocument(newOpportunity.getOriginalXML());
		String actualAgentNumber = AcordHelper.getAgentNumber(origXml);

		assertEquals(opportunityCreationRequest.getSubCode(), actualAgentNumber);
	}

	@Test
	void testUploadOpportunity_shouldNotReplaceOrigXmlAgentNumberWithOCRSubCode_whenOCRSubCodeIsNull() throws Exception {
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setSubCode(null);
		Document uploadedAcordDoc = opportunityCreationRequest.getUploadedACORD();
		String expectedAgentNumber = AcordHelper.getAgentNumber(uploadedAcordDoc);
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);

		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		Document origXml = XmlHelper.getDocument(newOpportunity.getOriginalXML());
		String actualAgentNumber = AcordHelper.getAgentNumber(origXml);

		assertEquals(expectedAgentNumber, actualAgentNumber);
	}

	@Test
	void testUploadOpportunity_shouldNotReplaceOrigXmlAgentNumberWithOCRSubCode_whenOCRSubCodeIsEmptyString() throws Exception {
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setSubCode("");
		Document uploadedAcordDoc = opportunityCreationRequest.getUploadedACORD();
		String expectedAgentNumber = AcordHelper.getAgentNumber(uploadedAcordDoc);
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);

		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		Document origXml = XmlHelper.getDocument(newOpportunity.getOriginalXML());
		String actualAgentNumber = AcordHelper.getAgentNumber(origXml);

		assertEquals(expectedAgentNumber, actualAgentNumber);
	}

	@Test
	void testUploadOpportunitySingleCAWithPaymentData() throws Exception{
		String path = "src/test/resources/xml/SingleCAEFTPaymentAuto_OneVehicle.xml";
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setSubCode("111111");
		opportunityCreationRequest.setLineType(LineType.Personal);
		opportunityCreationRequest.setUploadedACORD(getFileContents(path));
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);

		EFTPaymentAccountsResponse mockData = EFTPaymentAccountsResponse.builder()
				.bankAccountType("Checking")
				.financialDetailActId("***********")
				.acctHldrFullName("Test Test")
				.accHldrFirstName("Test")
				.accHldrLastName("Test")
				.bankRoutingNumber("********")
				.bankAccountNumber("************")
				.paymentAccountToken("2222222223333333334444")
				.build();

		lenient().when(eftPaymentAccountsService.getEFTPaymentAccountsDetails(any())).thenReturn(mockData);
		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		assertNotNull(newOpportunity);
	}

	@Test
	void testUploadOpportunitySingleCAWithoutPaymentData() throws Exception{
		String path = "src/test/resources/xml/SingleCAEFTPaymentAuto_OneVehicle.xml";
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setSubCode("111111");
		opportunityCreationRequest.setLineType(LineType.Personal);
		opportunityCreationRequest.setUploadedACORD(getFileContents(path));
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);

		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		assertNotNull(newOpportunity);
	}

	@Test
	void testReUploadOpportunitySingleCAOpportunityWithPaymentData() throws Exception{
		int oppId = 100100;
		String path = "src/test/resources/xml/SingleCAEFTPaymentAuto_OneVehicle.xml";
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setSubCode("111111");
		opportunityCreationRequest.setLineType(LineType.Personal);
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setUploadedACORD(getFileContents(path));
		opportunityCreationRequest.setData(getFileContents(path));
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);

		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		assertNotNull(newOpportunity);
		Mockito.verify(eftPaymentAccountsService, times(0)).getEFTPaymentAccountsDetails(any());
	}

	@Test
	void testReUploadOpportunitySingleCA_OpportunityWithoutPaymentData() throws Exception{
		int oppId = 100100;
		String originalXMLpath = "src/test/resources/xml/SingleCAEFTPaymentAuto_OneVehicle.xml";
		String dataXMLPath = "src/test/resources/xml/SingleCAAuto_WithPaymentLookupStatus.xml";
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setSubCode("111111");
		opportunityCreationRequest.setLineType(LineType.Personal);
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setUploadedACORD(getFileContents(originalXMLpath));
		opportunityCreationRequest.setData(getFileContents(dataXMLPath));
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);

		EFTPaymentAccountsResponse mockData = EFTPaymentAccountsResponse.builder()
				.bankAccountType("Checking")
				.financialDetailActId("***********")
				.acctHldrFullName("Test Test")
				.accHldrFirstName("Test")
				.accHldrLastName("Test")
				.bankRoutingNumber("********")
				.bankAccountNumber("************")
				.paymentAccountToken("2222222223333333334444")
				.build();

		lenient().when(eftPaymentAccountsService.getEFTPaymentAccountsDetails(any())).thenReturn(mockData);

		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		assertNotNull(newOpportunity);
		Mockito.verify(eftPaymentAccountsService).getEFTPaymentAccountsDetails(any());
	}

	@Test
	void testReUploadOpportunitySingleCAPaymentServiceReturnsNothing() throws Exception{
		int oppId = 100100;
		String originalXMLpath = "src/test/resources/xml/SingleCAEFTPaymentAuto_OneVehicle.xml";
		String dataXMLPath = "src/test/resources/xml/SingleCAAuto_WithPaymentLookupStatus.xml";
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setSubCode("111111");
		opportunityCreationRequest.setLineType(LineType.Personal);
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setUploadedACORD(getFileContents(originalXMLpath));
		opportunityCreationRequest.setData(getFileContents(dataXMLPath));
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);

		lenient().when(eftPaymentAccountsService.getEFTPaymentAccountsDetails(any())).thenReturn(null);

		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		assertNotNull(newOpportunity);
		Mockito.verify(eftPaymentAccountsService).getEFTPaymentAccountsDetails(any());
	}

	@Test
	void testReUploadOpportunitySingleCAOpportunityWithNoEFTStatus() throws Exception{
		int oppId = 100100;
		String path = "src/test/resources/xml/SingleCAEFTPaymentAuto_OneVehicle.xml";
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setSubCode("111111");
		opportunityCreationRequest.setLineType(LineType.Personal);
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		Document acord = getFileContents(path);
		opportunityCreationRequest.setUploadedACORD(acord);
		AcordHelper.setMigrationInfoPaymentLookupStatus(acord, Constants.PAYMENT_TYPE_IS_NOT_EFT);
		opportunityCreationRequest.setData(acord);
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);

		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		assertNotNull(newOpportunity);
		Mockito.verify(eftPaymentAccountsService, times(0)).getEFTPaymentAccountsDetails(any());
	}

	@Test
	void testReUploadOpportunitySingleCAOpportunityWithPaymentDataDoesNotExists() throws Exception{
		int oppId = 100100;
		String path = "src/test/resources/xml/SingleCAEFTPaymentAuto_OneVehicle.xml";
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setSubCode("111111");
		opportunityCreationRequest.setLineType(LineType.Personal);
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		Document acord = getFileContents(path);
		opportunityCreationRequest.setUploadedACORD(acord);
		AcordHelper.setMigrationInfoPaymentLookupStatus(acord, Constants.PAYMENT_DATA_DOES_NOT_EXIST);
		opportunityCreationRequest.setData(acord);
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);

		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		assertNotNull(newOpportunity);
		Mockito.verify(eftPaymentAccountsService, times(1)).getEFTPaymentAccountsDetails(any());
	}

	@Test
	void testReUploadOpportunitySingleCAOpportunityWithFailedLookup() throws Exception{
		int oppId = 100100;
		String path = "src/test/resources/xml/SingleCAEFTPaymentAuto_OneVehicle.xml";
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setSubCode("111111");
		opportunityCreationRequest.setLineType(LineType.Personal);
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		Document acord = getFileContents(path);
		opportunityCreationRequest.setUploadedACORD(acord);
		AcordHelper.setMigrationInfoPaymentLookupStatus(acord, Constants.FAILED_TO_LOOKUP_PAYMENT_DATA);
		opportunityCreationRequest.setData(acord);
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(false);

		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		assertNotNull(newOpportunity);
		Mockito.verify(eftPaymentAccountsService, times(1)).getEFTPaymentAccountsDetails(any());
	}

	@Test
	void testUploadOpportunity_shouldSetSpqeOrigXmlAgentNumberWithOCRSubCode_whenOCRSubCodeExists() throws Exception {
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setSubCode("111111");
		OpportunityBuilder builder = getUploadOpportunityHelperInstance(true);
		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		Document origXml = XmlHelper.getDocument(newOpportunity.getOriginalXML());
		String actualAgentNumber = AcordHelper.getAgentNumber(origXml);

		assertEquals(opportunityCreationRequest.getSubCode(), actualAgentNumber);
	}

	@Test
	void testUploadOpportunity_REUPLOAD_shouldSetOrigXmlAgentNumberWithOCRSubCode_whenOCRSubCodeExists() throws Exception {
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setExistingOpportunityID(oppId);
		opportunityCreationRequest.setSubCode("111111");
		Opportunity opportunity = new Opportunity();
		opportunity.setOpportunityId(oppId);
		opportunity.setOriginalXML(getFileContents());
		opportunity.setStatus(2);
		when(opportunityRepoHelper.findOpportunityById(anyInt())).thenReturn(opportunity);
		doAnswer(invocation -> invocation.getArgument(0))
				.when(addressCleanseService).addCountiesToLocations(any(Document.class));
		OpportunityBuilder builder = getReUploadOpportunityHelperInstance(false);
		Opportunity newOpportunity = builder.uploadOpportunity(opportunityCreationRequest);

		Document origXml = XmlHelper.getDocument(newOpportunity.getOriginalXML());
		String actualAgentNumber = AcordHelper.getAgentNumber(origXml);

		assertEquals(opportunityCreationRequest.getSubCode(), actualAgentNumber);
	}

	@Test
	@Disabled
	void testReuploadForSpqe() throws Exception {
		boolean isForSpqe = true;
		int oppId = 100100;
		OpportunityCreationRequest opportunityCreationRequest = buildOpportunityCreationRequestWithAutoXML();
		opportunityCreationRequest.setExistingOpportunityID(oppId);

		OpportunityBuilder builder = getReUploadOpportunityHelperInstance(isForSpqe);

		Opportunity opportunity = new Opportunity();
		opportunity.setOpportunityId(oppId);
		opportunity.setOriginalXML(getFileContents());
		opportunity.setStatus(2);

		when(opportunityRepoHelper.findOpportunityById(oppId)).thenReturn(opportunity);

		builder.uploadOpportunity(opportunityCreationRequest);
		assertTrue(builder.isForSPQE);

		verifyForReUpload(isForSpqe);
	}

	private void verifyForReUpload(boolean isForSpqe) throws Exception {
		InOrder order;
		if (isForSpqe) {
			order = Mockito.inOrder(opportunityRepoHelper, opportunityConstructor, quoteReportItemHelper);
			order.verify(opportunityRepoHelper, times(1)).findOpportunityById(1);
			verify(order);
			order.verify(opportunityRepoHelper, times(1)).save(any(Opportunity.class));
		} else {
			order = Mockito.inOrder(opportunityRepoHelper, opportunityConstructor, quoteReportItemHelper);
			order.verify(opportunityRepoHelper, times(1)).findOpportunityById(anyInt());
			verify(order);
			order.verify(opportunityRepoHelper, times(1)).updateOpportunity(any(Opportunity.class));
		}
		order.verify(quoteReportItemHelper).buildAndSaveQuoteReportItem(any(Opportunity.class), isNull(), isNull());
	}

	private Opportunity mockOpportunity() throws IOException {
		Opportunity opp = new Opportunity();
		String xml = getFileContents();
		opp.setData(xml);
		opp.setOriginalXML(xml);
		return opp;
	}
}
