name: 'Maven Build & Upload'
run-name: "Build: ${{ github.ref_name }}_${{ github.run_id }}_${{ github.run_attempt }}"

on:
  workflow_call:
    outputs:
      build-number:
        value: ${{ jobs.build.outputs.build-number }}
        description: 'The build number output from a successful build.'
  workflow_dispatch:

permissions:
  contents: read
  id-token: write

jobs:
  build-and-upload:
    runs-on: ubuntu-latest-internal
    timeout-minutes: 10
    steps:
      - name: Check out repository code
        uses: actions/checkout@v4

      - name: authenticate-image-registry
        uses: lmigtech/internal-actions/actions/authenticate-image-registry@v0
        with:
          forge-file: packages.yml

      - name: Inject Metadata
        id: metadata-inject
        uses: lmigtech/internal-actions/actions/metadata-inject@v0

      - name: Setup Java
        uses: lmigtech/internal-actions/actions/setup-java@v0
        with:
          distribution: 'liberica'
          java-version-file: .java-version
          cache: 'maven'
          forge-file: packages.yml

      - name: Package default version
        id: build-version
        uses: lmigtech/internal-actions/actions/package-default-version@v0
        with:
          package-identifier: ${{ steps.metadata-inject.outputs.artifact_key }}

      - name: Build with Ma<PERSON>
        run: mvn clean deploy -B -ntp -DskipTests=true
        env:
          MVNW_USERNAME: ${{ env.secret_packageRegistryUsername }}
          MVNW_PASSWORD: ${{ env.secret_packageRegistryToken}}

      - name: Prepare compiled file
        id: prepare-compiled-file
        uses: lmigtech/internal-actions/actions/java-prepare-compiled-file@v0
        with:
          file-name: ${{ steps.build-version.outputs.file-name }}

      - name: Create Manifest Package
        id: create-manifest-package
        uses: lmigtech/internal-actions/actions/package-create@v0
        with:
          name: ${{ steps.build-version.outputs.file-name }}-deployment-configurations
          flatten: true
          include: "./src/main/deployment/*"

      - name: Upload Manifest Package
        uses: lmigtech/internal-actions/actions/package-upload@v0
        with:
          file-name-or-pattern: ${{ steps.create-manifest-package.outputs.zip-file }}
          publish-build-info: false

      - name: Upload App Package
        uses: lmigtech/internal-actions/actions/package-upload@v0
        with:
          file-name-or-pattern: ${{ steps.prepare-compiled-file.outputs.compiled-file }}
