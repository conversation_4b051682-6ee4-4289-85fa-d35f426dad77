name: "OCF Deploy"
run-name: "🚀 OCF Deploy ${{ inputs.environment }}: ${{ inputs.build-number }}"

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "The environment to deploy to"
        required: true
        type: environment
      build-number:
        description: "The build number to deploy"
        required: true
        type: string

permissions:
  contents: read
  id-token: write

jobs:
  deploy:
    runs-on: ubuntu-latest-internal
    timeout-minutes: 10
    environment: ${{ inputs.environment }}
    concurrency:
      group: deploy-${{ inputs.environment }}
      cancel-in-progress: false
    steps:
      - name: Inject Metadata
        id: metadata-inject
        uses: lmigtech/internal-actions/actions/metadata-inject@v0

      - name: Download and Extract Package
        uses: lmigtech/internal-actions/actions/download-extract-generic-artifact@v0
        with:
          build-number: ${{ inputs.build-number }}
          file-name-or-pattern: ${{ steps.metadata-inject.outputs.artifact_key }}_${{ inputs.build-number }}-deployment-configurations.zip

      - name: Execute datadog_monitors SPI
        uses: lmigtech/internal-actions/actions/manifest-processor@v0
        with:
          name: datadog_monitors
          forge-file: monitors-${{ inputs.environment }}.json

      - name: Execute idp SPI
        uses: lmigtech/internal-actions/actions/manifest-processor@v0
        with:
          name: idp
          forge-file: idp-${{ inputs.environment }}.yml
          
      - name: Deploy to OCF using Internal Action
        uses: lmigtech/internal-actions/actions/cloudfoundry-deployment@v0
        with:
          forge-file: manifest-${{ inputs.environment }}.yml
          file-name: ${{ steps.metadata-inject.outputs.artifact_key }}_${{ inputs.build-number }}.jar
