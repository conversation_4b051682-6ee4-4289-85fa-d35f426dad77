name: "Publish Release"

# Description:
# Uses the Maven Release Plugin to prepare and perform a release, publishes it to Artifactory and then creates
# a GitHub Release. This workflow will only work on the master branch.
# The version released will be the version in the POM file with the "-SNAPSHOT" suffix removed.

on:
  workflow_dispatch:
    branches:
      - master

permissions:
  contents: read
  id-token: write

jobs:
  publish:
    runs-on: ubuntu-latest-internal
    timeout-minutes: 10
    steps:
      - name: Get CI token
        uses: lmigtech/internal-actions/actions/ci-token-create@v0
        id: ci-token-create
        with:
          permissions: |
            contents:write
          reason: version-bumping

      - name: Checkout
        uses: actions/checkout@v4
        with:
          token: ${{ steps.ci-token-create.outputs.token }}

      - name: Setup Java for release
        uses: lmigtech/internal-actions/actions/setup-java@v0
        with:
          distribution: liberica
          java-version-file: '.java-version'
          cache: 'maven'
          forge-file: packages.yml

      - name: Get version to be released
        run: |
          # Extract the version from pom.xml BEFORE release:prepare (while it still contains -SNAPSHOT)
          RELEASE_VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
          # Remove the "-SNAPSHOT" suffix to get the release version
          RELEASE_VERSION=${RELEASE_VERSION%-SNAPSHOT}
          echo "RELEASE_VERSION=${RELEASE_VERSION}" >> $GITHUB_ENV

      - name: Prepare maven release
        run: mvn -ntp release:prepare --batch-mode

      - name: Perform maven release
        run: mvn -ntp release:perform --batch-mode

      - name: Create GitHub Release
        uses: softprops/action-gh-release@v1
        with:
          name: ${{ env.RELEASE_VERSION }}
          tag_name: ${{ env.RELEASE_VERSION }}
          generate_release_notes: true
          token: ${{ steps.ci-token-create.outputs.token }}
