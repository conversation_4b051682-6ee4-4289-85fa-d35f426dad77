name: 'Evaluate'

on:
  workflow_call:
  workflow_dispatch:

permissions:
  contents: read
  id-token: write

jobs:
  run-tests:
    runs-on: ubuntu-latest-internal
    timeout-minutes: 10
    concurrency:
      group: run-tests-${{ github.ref }}
      cancel-in-progress: true
    steps:
      - name: Check out repository code
        uses: actions/checkout@v4

      - name: authenticate-image-registry
        uses: lmigtech/internal-actions/actions/authenticate-image-registry@v0
        with:
          forge-file: packages.yml

      - name: Setup Java
        uses: lmigtech/internal-actions/actions/setup-java@v0
        with:
          distribution: 'liberica'
          java-version-file: '.java-version'
          cache: 'maven'

      - name: Build with Maven
        run: mvn -ntp clean verify
        env:
          MVNW_USERNAME: ${{ env.secret_packageRegistryUsername }}
          MVNW_PASSWORD: ${{ env.secret_packageRegistryToken }}

      - name: Upload Coverage Report to Codacy
        uses: lmigtech/internal-actions/actions/coverage-upload@v0
        with:
          coverage-reports: target/site/jacoco/jacoco.xml
